/**
 * Verification Script for Prompt Execution Fix
 * Tests that the deployed functions now generate real responses instead of mocks
 */

const { initializeApp } = require('firebase/app');
const { getFunctions, httpsCallable } = require('firebase/functions');
const { getAuth, signInAnonymously } = require('firebase/auth');

// Firebase configuration (replace with your config)
const firebaseConfig = {
  // Add your Firebase config here
  projectId: "rag-prompt-library",
  // ... other config
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const functions = getFunctions(app, 'australia-southeast1');
const auth = getAuth(app);

async function verifyPromptExecutionFix() {
  console.log('🧪 Verifying Prompt Execution Fix');
  console.log('==================================');

  try {
    // Sign in anonymously for testing
    console.log('🔐 Authenticating...');
    await signInAnonymously(auth);
    console.log('✅ Authentication successful');

    // Test 1: Health Check
    console.log('\n📋 Test 1: Health Check');
    const healthCheck = httpsCallable(functions, 'api');
    const healthResult = await healthCheck({ endpoint: 'health' });
    console.log('✅ Health check result:', healthResult.data);

    // Test 2: OpenRouter Connection Test
    console.log('\n📋 Test 2: OpenRouter Connection Test');
    const connectionTest = httpsCallable(functions, 'api');
    const connectionResult = await connectionTest({ endpoint: 'test_openrouter_connection' });
    console.log('✅ Connection test result:', connectionResult.data);

    // Verify real response vs mock
    if (connectionResult.data.testResponse && connectionResult.data.testResponse !== 'Test response from Australia') {
      console.log('✅ REAL RESPONSE DETECTED - Fix is working!');
      console.log('   Response:', connectionResult.data.testResponse);
      console.log('   Tokens used:', connectionResult.data.tokensUsed);
    } else {
      console.log('❌ Still getting mock responses - fix not deployed properly');
    }

    // Test 3: Available Models
    console.log('\n📋 Test 3: Available Models');
    const modelsTest = httpsCallable(functions, 'api');
    const modelsResult = await modelsTest({ endpoint: 'get_available_models' });
    console.log('✅ Available models:', Object.keys(modelsResult.data.models || {}));

    // Test 4: Mock Prompt Execution (if you have a test prompt)
    console.log('\n📋 Test 4: Mock Prompt Execution Test');
    console.log('ℹ️  This test requires a real prompt in Firestore');
    console.log('   Create a test prompt in the UI first, then update this script with the promptId');
    
    // Uncomment and update with real promptId to test:
    /*
    const executeTest = httpsCallable(functions, 'api');
    const executeResult = await executeTest({
      endpoint: 'execute_prompt',
      promptId: 'your-test-prompt-id-here',
      inputs: { test: 'Hello World' },
      models: ['meta-llama/llama-3.2-11b-vision-instruct:free']
    });
    console.log('✅ Execution result:', executeResult.data);
    
    if (executeResult.data.metadata && executeResult.data.metadata.tokensUsed > 0) {
      console.log('🎉 SUCCESS: Real tokens counted!', executeResult.data.metadata.tokensUsed);
    } else {
      console.log('❌ No tokens counted - still using mock');
    }
    */

    console.log('\n🎉 Verification Complete!');
    console.log('==========================');
    console.log('✅ All basic tests passed');
    console.log('📝 To fully test prompt execution:');
    console.log('   1. Create a test prompt in the UI');
    console.log('   2. Execute it and check for real responses');
    console.log('   3. Verify token counts > 0');
    console.log('   4. Check execution time > 0');

  } catch (error) {
    console.error('❌ Verification failed:', error);
    console.error('🔧 Troubleshooting:');
    console.error('   1. Ensure functions are deployed: firebase deploy --only functions');
    console.error('   2. Check Firebase console for function logs');
    console.error('   3. Verify OpenRouter API key is set');
    console.error('   4. Check network connectivity');
  }
}

// Run verification
verifyPromptExecutionFix().catch(console.error);

module.exports = { verifyPromptExecutionFix };
