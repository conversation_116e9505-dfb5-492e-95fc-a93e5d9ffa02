# GitHub Secrets Configuration Guide
## Required for Fixed CI/CD Firebase Deployment

This guide helps you configure the GitHub repository secrets required for the fixed CI/CD pipeline to properly deploy the Firebase Functions with real OpenRouter LLM integration.

---

## 🔑 Required Secrets

### **1. Firebase Service Accounts**

#### **FIREBASE_SERVICE_ACCOUNT** (Staging)
```bash
# 1. Go to Firebase Console > Project Settings > Service Accounts
# 2. Click "Generate new private key" for staging project
# 3. Download the JSON file
# 4. Copy the entire JSON content
# 5. In GitHub: Settings > Secrets > Actions > New repository secret
# Name: FIREBASE_SERVICE_ACCOUNT
# Value: [paste entire JSON content]
```

#### **FIREBASE_SERVICE_ACCOUNT_PROD** (Production)
```bash
# Same process as above but for production project
# Name: FIREBASE_SERVICE_ACCOUNT_PROD
# Value: [paste production JSON content]
```

### **2. OpenRouter API Keys**

#### **OPENROUTER_API_KEY** (Staging)
```bash
# 1. Go to https://openrouter.ai/keys
# 2. Create a new API key for staging
# 3. In GitHub: Settings > Secrets > Actions > New repository secret
# Name: OPENROUTER_API_KEY
# Value: sk-or-v1-your-staging-key-here
```

#### **OPENROUTER_API_KEY_PROD** (Production)
```bash
# Same process but for production
# Name: OPENROUTER_API_KEY_PROD
# Value: sk-or-v1-your-production-key-here
```

### **3. Optional Notification**

#### **SLACK_WEBHOOK** (Optional)
```bash
# For deployment notifications
# Name: SLACK_WEBHOOK
# Value: https://hooks.slack.com/services/your/webhook/url
```

---

## 🚀 Quick Setup Commands

### **Using GitHub CLI (Recommended)**

```bash
# Install GitHub CLI if not already installed
# https://cli.github.com/

# Set Firebase service account for staging
gh secret set FIREBASE_SERVICE_ACCOUNT < path/to/staging-service-account.json

# Set Firebase service account for production
gh secret set FIREBASE_SERVICE_ACCOUNT_PROD < path/to/production-service-account.json

# Set OpenRouter API keys
gh secret set OPENROUTER_API_KEY --body "sk-or-v1-your-staging-key"
gh secret set OPENROUTER_API_KEY_PROD --body "sk-or-v1-your-production-key"

# Optional: Set Slack webhook
gh secret set SLACK_WEBHOOK --body "https://hooks.slack.com/services/your/webhook"
```

### **Using GitHub Web Interface**

1. Go to your repository on GitHub
2. Click **Settings** tab
3. Click **Secrets and variables** > **Actions**
4. Click **New repository secret**
5. Add each secret with the name and value specified above

---

## 🔍 Verification

### **Check Secrets Are Set**
```bash
# List all secrets (values won't be shown for security)
gh secret list
```

Expected output:
```
FIREBASE_SERVICE_ACCOUNT
FIREBASE_SERVICE_ACCOUNT_PROD
OPENROUTER_API_KEY
OPENROUTER_API_KEY_PROD
SLACK_WEBHOOK
```

### **Test Deployment**
After setting up secrets:

1. **Push to main branch** to trigger deployment
2. **Check GitHub Actions** tab for deployment progress
3. **Verify functions deployed** using the verification commands in the main report

---

## 🛡️ Security Best Practices

### **Service Account Permissions**
Ensure Firebase service accounts have minimal required permissions:
- Firebase Functions Admin
- Firebase Hosting Admin
- Cloud Functions Admin

### **API Key Management**
- Use separate API keys for staging and production
- Set spending limits on OpenRouter keys
- Rotate keys regularly
- Monitor usage in OpenRouter dashboard

### **Secret Rotation**
- Rotate service accounts every 90 days
- Update API keys if compromised
- Use environment-specific keys

---

## 🚨 Troubleshooting

### **Common Issues**

#### **"Invalid service account" error**
```bash
# Check JSON format is valid
cat service-account.json | jq .

# Ensure no extra whitespace or characters
# Re-download from Firebase Console if needed
```

#### **"Invalid API key" error**
```bash
# Test API key manually
curl -H "Authorization: Bearer sk-or-v1-your-key" \
  https://openrouter.ai/api/v1/models

# Check key has sufficient credits
# Verify key is not rate-limited
```

#### **"Permission denied" error**
```bash
# Check service account has required roles:
# - Firebase Admin SDK Administrator Service Agent
# - Cloud Functions Admin
# - Firebase Hosting Admin
```

---

## 📋 Checklist

Before triggering deployment, ensure:

- [ ] **FIREBASE_SERVICE_ACCOUNT** secret is set with valid staging JSON
- [ ] **FIREBASE_SERVICE_ACCOUNT_PROD** secret is set with valid production JSON
- [ ] **OPENROUTER_API_KEY** secret is set with valid staging key
- [ ] **OPENROUTER_API_KEY_PROD** secret is set with valid production key
- [ ] Service accounts have required Firebase permissions
- [ ] OpenRouter API keys have sufficient credits
- [ ] Firebase projects exist and are accessible
- [ ] GitHub Actions are enabled for the repository

---

## 🎯 Next Steps

1. **Configure all required secrets** using this guide
2. **Push to main branch** to trigger the fixed CI/CD pipeline
3. **Monitor deployment** in GitHub Actions tab
4. **Verify functions work** using the verification commands
5. **Test prompt execution** in the application UI

The fixed CI/CD pipeline will now properly deploy the real OpenRouter LLM integration instead of mock responses!
