/**
 * Test script to check the document status API endpoint
 */

async function testDocumentStatusAPI() {
    console.log('🔍 Testing Document Status API Endpoint...\n');

    // Test cases
    const testCases = [
        {
            name: 'Non-existent Job ID',
            jobId: 'test-job-123',
            expectedStatus: 404
        },
        {
            name: 'Invalid Job ID Format',
            jobId: 'invalid-job-format',
            expectedStatus: 404
        },
        {
            name: 'Empty Job ID',
            jobId: '',
            expectedStatus: 404
        }
    ];

    for (const testCase of testCases) {
        console.log(`\n📋 Test Case: ${testCase.name}`);
        console.log(`🔗 Job ID: "${testCase.jobId}"`);
        
        try {
            const url = `http://localhost:3000/api/ai/document-status/${testCase.jobId}`;
            console.log(`🌐 URL: ${url}`);
            
            const response = await fetch(url);
            console.log(`📊 Status Code: ${response.status}`);
            
            if (response.ok) {
                const data = await response.json();
                console.log(`✅ Response Data:`, JSON.stringify(data, null, 2));
                
                // Check if response has expected structure
                if (data.success !== undefined) {
                    console.log(`🔍 API Success: ${data.success}`);
                    console.log(`🔍 Job Status: ${data.status || 'N/A'}`);
                    console.log(`🔍 Document ID: ${data.document_id || 'N/A'}`);
                } else {
                    console.log(`⚠️  Unexpected response structure`);
                }
            } else {
                const errorText = await response.text();
                console.log(`❌ Error Response: ${errorText}`);
            }
            
        } catch (error) {
            console.log(`💥 Network Error: ${error.message}`);
            
            if (error.message.includes('fetch')) {
                console.log(`🔧 Suggestion: Check if the backend server is running`);
                console.log(`🔧 Expected backend URL: http://localhost:3000/api/ai/document-status/`);
            }
        }
        
        console.log('─'.repeat(50));
    }
}

async function checkBackendHealth() {
    console.log('\n🏥 Checking Backend Health...\n');
    
    const healthEndpoints = [
        'http://localhost:3000/api/health',
        'http://localhost:3000/health',
        'http://localhost:3000/api/ai/health',
        'http://localhost:5000/api/health', // Alternative port
        'http://localhost:8080/api/health'  // Alternative port
    ];
    
    for (const endpoint of healthEndpoints) {
        try {
            console.log(`🔍 Checking: ${endpoint}`);
            const response = await fetch(endpoint, { 
                method: 'GET',
                timeout: 5000 
            });
            
            if (response.ok) {
                const data = await response.text();
                console.log(`✅ Backend found at: ${endpoint}`);
                console.log(`📊 Response: ${data}`);
                return endpoint;
            }
        } catch (error) {
            console.log(`❌ Not available: ${endpoint}`);
        }
    }
    
    console.log(`\n⚠️  No backend server found on common ports`);
    console.log(`🔧 Suggestions:`);
    console.log(`   1. Start the Firebase Functions emulator`);
    console.log(`   2. Check if backend is running on a different port`);
    console.log(`   3. Verify Firebase configuration`);
    
    return null;
}

async function simulateStuckDocument() {
    console.log('\n🔄 Simulating Stuck Document Scenario...\n');
    
    // This simulates what happens when a document gets stuck
    const mockJobId = 'stuck-job-' + Date.now();
    let attempts = 0;
    const maxAttempts = 10; // Reduced for demo
    
    console.log(`📄 Simulating job: ${mockJobId}`);
    
    const pollStatus = async () => {
        attempts++;
        console.log(`📊 Poll attempt ${attempts}/${maxAttempts}`);
        
        // Simulate the stuck scenario - status never changes
        const mockResponse = {
            success: true,
            job_id: mockJobId,
            status: 'processing', // Always processing - this is the bug!
            document_id: 'doc_123',
            progress: 50,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };
        
        console.log(`🔍 Mock Response:`, JSON.stringify(mockResponse, null, 2));
        
        // Check termination conditions
        if (mockResponse.status === 'completed') {
            console.log(`✅ Job completed successfully`);
            return;
        } else if (mockResponse.status === 'failed') {
            console.log(`❌ Job failed`);
            return;
        } else if (attempts >= maxAttempts) {
            console.log(`⏰ Timeout: Job stuck after ${maxAttempts} attempts`);
            console.log(`🚨 This is the infinite spinning icon bug!`);
            return;
        }
        
        // Continue polling
        console.log(`⏳ Status still "${mockResponse.status}", continuing to poll...`);
        setTimeout(pollStatus, 2000); // Poll every 2 seconds for demo
    };
    
    pollStatus();
}

async function main() {
    console.log('🚀 Document Processing API Test Suite');
    console.log('=====================================\n');
    
    // Check if backend is available
    const backendUrl = await checkBackendHealth();
    
    if (backendUrl) {
        // Test the actual API
        await testDocumentStatusAPI();
    } else {
        console.log('\n🔧 Backend not available, running simulation instead...');
    }
    
    // Always run the simulation to demonstrate the issue
    await simulateStuckDocument();
    
    console.log('\n📋 Summary of Findings:');
    console.log('========================');
    console.log('1. ✅ Identified the infinite polling issue');
    console.log('2. ✅ Demonstrated how status gets stuck at "processing"');
    console.log('3. ✅ Showed timeout protection is needed');
    console.log('4. 🔧 Backend API needs to be tested with real data');
    console.log('\n💡 Next Steps:');
    console.log('- Start the backend server to test real API responses');
    console.log('- Check Firestore for stuck document processing jobs');
    console.log('- Implement the enhanced polling mechanism');
    console.log('- Add admin tools for manual intervention');
}

// Run the test
main().catch(console.error);
