/**
 * Test Execute Prompt Deployment Status
 * Run this script to verify if your Firebase Functions are properly deployed
 */

const testDeployment = async () => {
    console.log('🔍 Testing Execute Prompt Deployment Status...');
    
    const projectId = 'rag-prompt-library';
    const region = 'australia-southeast1';
    
    // Test endpoints
    const endpoints = [
        {
            name: 'Health Check',
            url: `https://${region}-${projectId}.cloudfunctions.net/api`,
            method: 'POST',
            body: { data: { endpoint: 'health' } }
        },
        {
            name: 'OpenRouter Test',
            url: `https://${region}-${projectId}.cloudfunctions.net/api`,
            method: 'POST',
            body: { data: { endpoint: 'test_openrouter_connection' } }
        }
    ];
    
    for (const endpoint of endpoints) {
        try {
            console.log(`\n🧪 Testing ${endpoint.name}...`);
            
            const response = await fetch(endpoint.url, {
                method: endpoint.method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(endpoint.body)
            });
            
            const data = await response.json();
            
            if (response.ok) {
                console.log(`✅ ${endpoint.name} - SUCCESS`);
                console.log(`📊 Response:`, data);
            } else {
                console.log(`❌ ${endpoint.name} - FAILED`);
                console.log(`📊 Error:`, data);
            }
            
        } catch (error) {
            console.log(`❌ ${endpoint.name} - ERROR`);
            console.log(`📊 Error:`, error.message);
        }
    }
    
    console.log('\n📋 Deployment Test Complete');
};

// Run the test
testDeployment();
