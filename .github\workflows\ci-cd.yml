name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.11'
  REGISTRY: gcr.io
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}

jobs:
  # Frontend Tests
  frontend-test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run linting
        run: npm run lint
      
      - name: Run type checking
        run: npm run type-check
      
      - name: Run tests
        run: npm test -- --coverage --watchAll=false
      
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: frontend

  # Backend Tests
  backend-test:
    runs-on: ubuntu-latest
    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: 'pip'
      
      - name: Install dependencies
        run: |
          cd functions
          pip install -r requirements.txt
          pip install pytest pytest-cov pytest-asyncio
      
      - name: Run linting
        run: |
          cd functions
          flake8 src/ --max-line-length=100
          black --check src/
      
      - name: Run type checking
        run: |
          cd functions
          mypy src/ --ignore-missing-imports
      
      - name: Run tests
        env:
          REDIS_URL: redis://localhost:6379
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
        run: |
          cd functions
          pytest tests/ -v --cov=src --cov-report=xml
      
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./functions/coverage.xml
          flags: backend

  # Security Scanning
  security-scan:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'
      
      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'trivy-results.sarif'



  # Deploy to Firebase Staging
  deploy-firebase-staging:
    needs: [frontend-test, backend-test]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: staging

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js for Firebase Functions
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: functions/package-lock.json

      - name: Install Firebase CLI
        run: npm install -g firebase-tools

      - name: Install Functions Dependencies
        run: |
          cd functions
          npm ci

      - name: Build Frontend for Hosting
        run: |
          cd frontend
          npm ci
          npm run build

      - name: Setup Firebase Authentication
        run: |
          echo '${{ secrets.FIREBASE_SERVICE_ACCOUNT }}' > $HOME/firebase-service-account.json
          export GOOGLE_APPLICATION_CREDENTIALS=$HOME/firebase-service-account.json

      - name: Set Firebase Functions Environment Variables
        run: |
          export GOOGLE_APPLICATION_CREDENTIALS=$HOME/firebase-service-account.json
          firebase functions:config:set \
            openrouter.api_key="${{ secrets.OPENROUTER_API_KEY }}" \
            app.environment="staging" \
            --project rag-prompt-library-staging

      - name: Deploy to Firebase Staging
        run: |
          export GOOGLE_APPLICATION_CREDENTIALS=$HOME/firebase-service-account.json
          firebase deploy --only hosting,functions --project rag-prompt-library-staging

      - name: Run Firebase Functions Smoke Tests
        run: |
          # Test health endpoint
          sleep 30  # Wait for functions to be ready
          curl -f "https://australia-southeast1-rag-prompt-library-staging.cloudfunctions.net/api" \
            -H "Content-Type: application/json" \
            -d '{"data":{"endpoint":"health"}}'

          # Test OpenRouter connection
          curl -f "https://australia-southeast1-rag-prompt-library-staging.cloudfunctions.net/api" \
            -H "Content-Type: application/json" \
            -d '{"data":{"endpoint":"test_openrouter_connection"}}'

  # Deploy to Firebase Production
  deploy-firebase-production:
    needs: [deploy-firebase-staging]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js for Firebase Functions
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: functions/package-lock.json

      - name: Install Firebase CLI
        run: npm install -g firebase-tools

      - name: Install Functions Dependencies
        run: |
          cd functions
          npm ci

      - name: Build Frontend for Hosting
        run: |
          cd frontend
          npm ci
          npm run build

      - name: Setup Firebase Authentication
        run: |
          echo '${{ secrets.FIREBASE_SERVICE_ACCOUNT_PROD }}' > $HOME/firebase-service-account.json
          export GOOGLE_APPLICATION_CREDENTIALS=$HOME/firebase-service-account.json

      - name: Set Firebase Functions Environment Variables
        run: |
          export GOOGLE_APPLICATION_CREDENTIALS=$HOME/firebase-service-account.json
          firebase functions:config:set \
            openrouter.api_key="${{ secrets.OPENROUTER_API_KEY_PROD }}" \
            app.environment="production" \
            --project rag-prompt-library

      - name: Deploy to Firebase Production
        run: |
          export GOOGLE_APPLICATION_CREDENTIALS=$HOME/firebase-service-account.json
          firebase deploy --only hosting,functions --project rag-prompt-library

      - name: Run Production Health Checks
        run: |
          # Test health endpoint
          sleep 30  # Wait for functions to be ready
          curl -f "https://australia-southeast1-rag-prompt-library.cloudfunctions.net/api" \
            -H "Content-Type: application/json" \
            -d '{"data":{"endpoint":"health"}}'

          # Test OpenRouter connection with real API
          curl -f "https://australia-southeast1-rag-prompt-library.cloudfunctions.net/api" \
            -H "Content-Type: application/json" \
            -d '{"data":{"endpoint":"test_openrouter_connection"}}'

  # Notify on completion
  notify:
    needs: [deploy-firebase-production]
    runs-on: ubuntu-latest
    if: always()

    steps:
      - name: Notify Slack
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        if: always()
