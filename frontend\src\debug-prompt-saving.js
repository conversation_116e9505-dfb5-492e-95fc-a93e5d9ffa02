// Comprehensive debugging script for prompt saving issues
import { auth, db } from './config/firebase';
import { onAuthStateChanged, signInAnonymously } from 'firebase/auth';
import { collection, addDoc, getDocs, serverTimestamp, doc, getDoc } from 'firebase/firestore';

// Debug configuration
const DEBUG_CONFIG = {
  enableLogging: true,
  testWithAnonymousUser: true,
  testPromptData: {
    title: 'Debug Test Prompt',
    content: 'This is a test prompt to debug the saving functionality.',
    description: 'Test prompt for debugging purposes',
    category: 'General',
    tags: ['debug', 'test'],
    isPublic: false,
    variables: []
  }
};

// Logging utility
const log = (level, message, data = null) => {
  if (!DEBUG_CONFIG.enableLogging) return;
  
  const timestamp = new Date().toISOString();
  const prefix = `[${timestamp}] [${level.toUpperCase()}]`;
  
  if (data) {
    console.log(`${prefix} ${message}`, data);
  } else {
    console.log(`${prefix} ${message}`);
  }
};

// Test Firebase configuration
export const testFirebaseConfig = () => {
  log('info', 'Testing Firebase configuration...');
  
  try {
    log('info', 'Firebase Auth instance:', auth);
    log('info', 'Firebase Firestore instance:', db);
    log('info', 'Current user:', auth.currentUser);
    
    // Test environment variables
    const config = {
      apiKey: import.meta.env.VITE_FIREBASE_API_KEY ? '✅ Set' : '❌ Missing',
      authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN ? '✅ Set' : '❌ Missing',
      projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID ? '✅ Set' : '❌ Missing',
      storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET ? '✅ Set' : '❌ Missing',
      messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID ? '✅ Set' : '❌ Missing',
      appId: import.meta.env.VITE_FIREBASE_APP_ID ? '✅ Set' : '❌ Missing'
    };
    
    log('info', 'Environment variables:', config);
    
    return true;
  } catch (error) {
    log('error', 'Firebase configuration test failed:', error);
    return false;
  }
};

// Test authentication
export const testAuthentication = async () => {
  log('info', 'Testing authentication...');
  
  return new Promise((resolve) => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (user) {
        log('info', 'User is authenticated:', {
          uid: user.uid,
          email: user.email,
          displayName: user.displayName,
          isAnonymous: user.isAnonymous
        });
        unsubscribe();
        resolve(user);
      } else {
        log('info', 'No user authenticated, attempting anonymous sign-in...');
        
        if (DEBUG_CONFIG.testWithAnonymousUser) {
          try {
            const result = await signInAnonymously(auth);
            log('info', 'Anonymous sign-in successful:', result.user.uid);
            unsubscribe();
            resolve(result.user);
          } catch (error) {
            log('error', 'Anonymous sign-in failed:', error);
            unsubscribe();
            resolve(null);
          }
        } else {
          unsubscribe();
          resolve(null);
        }
      }
    });
  });
};

// Test Firestore permissions
export const testFirestorePermissions = async (user) => {
  if (!user) {
    log('error', 'Cannot test Firestore permissions without authenticated user');
    return false;
  }
  
  log('info', 'Testing Firestore permissions for user:', user.uid);
  
  try {
    // Test reading user's prompts collection
    const promptsRef = collection(db, 'users', user.uid, 'prompts');
    log('info', 'Created collection reference:', promptsRef.path);
    
    const querySnapshot = await getDocs(promptsRef);
    log('info', 'Successfully read prompts collection. Count:', querySnapshot.size);
    
    querySnapshot.forEach((doc) => {
      log('info', 'Existing prompt:', { id: doc.id, data: doc.data() });
    });
    
    return true;
  } catch (error) {
    log('error', 'Firestore permissions test failed:', error);
    return false;
  }
};

// Test prompt creation
export const testPromptCreation = async (user) => {
  if (!user) {
    log('error', 'Cannot test prompt creation without authenticated user');
    return false;
  }
  
  log('info', 'Testing prompt creation for user:', user.uid);
  
  try {
    const promptsRef = collection(db, 'users', user.uid, 'prompts');
    
    const testPrompt = {
      ...DEBUG_CONFIG.testPromptData,
      createdBy: user.uid,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      version: 1
    };
    
    log('info', 'Attempting to create prompt:', testPrompt);
    
    const docRef = await addDoc(promptsRef, testPrompt);
    log('info', 'Prompt created successfully with ID:', docRef.id);
    
    // Verify the prompt was saved
    const savedDoc = await getDoc(docRef);
    if (savedDoc.exists()) {
      log('info', 'Prompt verification successful:', savedDoc.data());
      return docRef.id;
    } else {
      log('error', 'Prompt was not saved properly');
      return false;
    }
    
  } catch (error) {
    log('error', 'Prompt creation test failed:', error);
    return false;
  }
};

// Run comprehensive test
export const runComprehensiveTest = async () => {
  log('info', '🚀 Starting comprehensive prompt saving test...');
  
  try {
    // Step 1: Test Firebase configuration
    const configOk = testFirebaseConfig();
    if (!configOk) {
      log('error', 'Firebase configuration test failed');
      return false;
    }
    
    // Step 2: Test authentication
    const user = await testAuthentication();
    if (!user) {
      log('error', 'Authentication test failed');
      return false;
    }
    
    // Step 3: Test Firestore permissions
    const permissionsOk = await testFirestorePermissions(user);
    if (!permissionsOk) {
      log('error', 'Firestore permissions test failed');
      return false;
    }
    
    // Step 4: Test prompt creation
    const promptId = await testPromptCreation(user);
    if (!promptId) {
      log('error', 'Prompt creation test failed');
      return false;
    }
    
    log('info', '🎉 All tests passed! Prompt saving should work correctly.');
    log('info', 'Test prompt ID:', promptId);
    
    return true;
    
  } catch (error) {
    log('error', 'Comprehensive test failed:', error);
    return false;
  }
};

// Make functions available globally for browser console testing
if (typeof window !== 'undefined') {
  window.debugPromptSaving = {
    testFirebaseConfig,
    testAuthentication,
    testFirestorePermissions,
    testPromptCreation,
    runComprehensiveTest
  };
  
  log('info', '🧪 Debug functions available at window.debugPromptSaving');
}
