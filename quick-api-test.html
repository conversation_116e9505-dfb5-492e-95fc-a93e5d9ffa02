<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick API Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; }
        .log { background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; margin: 10px 0; font-family: monospace; font-size: 12px; white-space: pre-wrap; }
        button { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; background: #007bff; color: white; }
        .success { background: #d4edda; color: #155724; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🔧 Quick API Test</h1>
    <p>Testing the updated routing...</p>
    
    <button onclick="testAPI()">Test Document Status API</button>
    <button onclick="testHealth()">Test Health API</button>
    
    <div id="result" class="log">Ready to test...</div>

    <script>
        function log(message) {
            const result = document.getElementById('result');
            const timestamp = new Date().toLocaleTimeString();
            result.textContent += `[${timestamp}] ${message}\n`;
        }

        async function testAPI() {
            document.getElementById('result').textContent = '';
            log('🔍 Testing document status API...');
            
            try {
                const response = await fetch('https://rag-prompt-library.web.app/api/ai/document-status/test-job-123');
                const data = await response.json();
                
                log(`📊 Status: ${response.status}`);
                log(`📋 Response: ${JSON.stringify(data, null, 2)}`);
                
                if (response.status === 404 && data.error && data.error.includes('Document not found')) {
                    log('✅ SUCCESS: API routing is working! (404 is expected for non-existent document)');
                    document.body.insertAdjacentHTML('beforeend', '<div class="success">🎉 API routing fixed! The endpoint is working correctly.</div>');
                } else if (data.success !== undefined) {
                    log('✅ SUCCESS: API is responding with proper JSON structure!');
                    document.body.insertAdjacentHTML('beforeend', '<div class="success">🎉 API is working correctly!</div>');
                } else {
                    log('⚠️ Unexpected response structure');
                }
                
            } catch (error) {
                log(`❌ Error: ${error.message}`);
                if (error.message.includes('Unexpected token')) {
                    document.body.insertAdjacentHTML('beforeend', '<div class="error">❌ Still getting HTML responses - routing not fixed yet</div>');
                }
            }
        }

        async function testHealth() {
            log('🏥 Testing health endpoint...');
            
            try {
                const response = await fetch('https://rag-prompt-library.web.app/api/health');
                const data = await response.json();
                
                log(`📊 Status: ${response.status}`);
                log(`📋 Response: ${JSON.stringify(data, null, 2)}`);
                
                if (data.status === 'healthy') {
                    log('✅ SUCCESS: Health endpoint working!');
                }
                
            } catch (error) {
                log(`❌ Health endpoint error: ${error.message}`);
            }
        }

        // Auto-test on load
        setTimeout(testAPI, 1000);
    </script>
</body>
</html>
