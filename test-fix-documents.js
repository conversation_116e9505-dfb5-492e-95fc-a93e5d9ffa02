const { initializeApp } = require('firebase/app');
const { getAuth, signInAnonymously } = require('firebase/auth');
const { getFunctions, httpsCallable } = require('firebase/functions');

// Firebase config
const firebaseConfig = {
  apiKey: "AIzaSyDNjqBxZlwGZl_3n8Z8Z8Z8Z8Z8Z8Z8Z8Z",
  authDomain: "rag-prompt-library.firebaseapp.com",
  projectId: "rag-prompt-library",
  storageBucket: "rag-prompt-library.appspot.com",
  messagingSenderId: "743998930129",
  appId: "1:743998930129:web:abc123def456"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const functions = getFunctions(app, 'australia-southeast1');

async function fixDocumentStatuses() {
  try {
    console.log('🔐 Authenticating...');
    await signInAnonymously(auth);
    console.log('✅ Authentication successful');

    console.log('🔧 Calling fix_document_statuses function...');
    const fixDocuments = httpsCallable(functions, 'fix_document_statuses');
    const result = await fixDocuments();

    console.log('📊 Result:', result.data);

    if (result.data.success) {
      console.log('🎉 SUCCESS!');
      console.log(`📄 Fixed ${result.data.documentsFixed} documents`);
      if (result.data.documentNames && result.data.documentNames.length > 0) {
        console.log('📋 Documents fixed:');
        result.data.documentNames.forEach(name => console.log(`  - ${name}`));
      }
      console.log('');
      console.log('✅ Your documents should now appear in the RAG selection dropdown!');
      console.log('🔄 Refresh your prompt execution page to see the documents.');
    } else {
      console.log('❌ Fix failed:', result.data.error);
    }

  } catch (error) {
    console.error('❌ Error:', error);
  }
}

fixDocumentStatuses();
