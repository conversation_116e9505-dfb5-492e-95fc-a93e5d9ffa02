<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive Debug - RAG Prompt Library</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .section { background: #f8f9fa; padding: 20px; border-radius: 5px; border-left: 4px solid #007bff; }
        .log { background: #000; color: #00ff00; font-family: monospace; font-size: 12px; padding: 15px; border-radius: 5px; max-height: 300px; overflow-y: auto; margin: 10px 0; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        .status { padding: 10px; border-radius: 5px; margin: 5px 0; }
        .status.success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .status.error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .status.warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .status.info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Comprehensive Debug Analysis</h1>
        <p>This page provides a comprehensive analysis of the RAG Prompt Library application to identify why prompts are not being saved.</p>
    </div>

    <div class="grid">
        <div class="container">
            <h2>🧪 Automated Tests</h2>
            <div class="section">
                <h3>Quick Diagnostics</h3>
                <button onclick="runQuickDiagnostics()">Run Quick Diagnostics</button>
                <button onclick="testLocalApp()">Test Local App</button>
                <button onclick="testProductionApp()">Test Production App</button>
                <button onclick="clearResults()">Clear Results</button>
                <div id="test-results"></div>
            </div>
        </div>

        <div class="container">
            <h2>📊 System Status</h2>
            <div class="section">
                <h3>Environment Check</h3>
                <div id="environment-status">
                    <div class="status info">Checking environment...</div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>🔧 Manual Testing Guide</h2>
        <div class="grid">
            <div class="section">
                <h3>Step 1: Authentication Test</h3>
                <ol>
                    <li>Open <a href="http://localhost:3000" target="_blank">localhost:3000</a></li>
                    <li>Sign in with Google or create an account</li>
                    <li>Verify you reach the dashboard</li>
                    <li>Check browser console for auth errors</li>
                </ol>
                <p><strong>Expected:</strong> Successful login and dashboard access</p>
            </div>

            <div class="section">
                <h3>Step 2: Prompt Creation Test</h3>
                <ol>
                    <li>Navigate to Prompts page</li>
                    <li>Click "Create from Scratch"</li>
                    <li>Fill in: Title, Content, Category</li>
                    <li>Click "Save Prompt"</li>
                    <li>Check if prompt appears in list</li>
                </ol>
                <p><strong>Expected:</strong> Prompt saved and visible in list</p>
            </div>

            <div class="section">
                <h3>Step 3: Console Debugging</h3>
                <ol>
                    <li>Open browser console (F12)</li>
                    <li>Run: <code>window.runComprehensiveTest()</code></li>
                    <li>Check for error messages</li>
                    <li>Look for Firebase/Firestore errors</li>
                </ol>
                <p><strong>Expected:</strong> All tests pass without errors</p>
            </div>

            <div class="section">
                <h3>Step 4: Network Analysis</h3>
                <ol>
                    <li>Open Network tab in DevTools</li>
                    <li>Try to save a prompt</li>
                    <li>Look for failed requests</li>
                    <li>Check for CORS errors</li>
                </ol>
                <p><strong>Expected:</strong> Successful Firestore API calls</p>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>🚨 Common Issues & Solutions</h2>
        <div class="grid">
            <div class="section">
                <h3>Authentication Issues</h3>
                <ul>
                    <li><strong>User not authenticated:</strong> Check auth state persistence</li>
                    <li><strong>Google sign-in fails:</strong> Check OAuth configuration</li>
                    <li><strong>Anonymous auth blocked:</strong> Enable in Firebase console</li>
                </ul>
            </div>

            <div class="section">
                <h3>Firestore Issues</h3>
                <ul>
                    <li><strong>Permission denied:</strong> Check Firestore security rules</li>
                    <li><strong>Collection not found:</strong> Verify collection path</li>
                    <li><strong>Index missing:</strong> Check Firestore indexes</li>
                </ul>
            </div>

            <div class="section">
                <h3>Network Issues</h3>
                <ul>
                    <li><strong>CORS errors:</strong> Check Firebase hosting configuration</li>
                    <li><strong>API key invalid:</strong> Verify Firebase configuration</li>
                    <li><strong>Network timeout:</strong> Check internet connection</li>
                </ul>
            </div>

            <div class="section">
                <h3>Code Issues</h3>
                <ul>
                    <li><strong>JavaScript errors:</strong> Check browser console</li>
                    <li><strong>React state issues:</strong> Check component state</li>
                    <li><strong>Async/await errors:</strong> Check promise handling</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>📋 Debug Log</h2>
        <div id="debug-log" class="log">
            <div>Debug log initialized...</div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            const entry = `<div class="${type}">[${timestamp}] ${message}</div>`;
            logDiv.innerHTML += entry;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStatus(id, message, type = 'info') {
            const statusDiv = document.getElementById(id);
            if (statusDiv) {
                statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
            }
        }

        function runQuickDiagnostics() {
            log('🚀 Starting quick diagnostics...', 'info');
            
            // Check if we're on localhost
            const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
            log(`Environment: ${isLocalhost ? 'Local Development' : 'Production/Other'}`, 'info');
            
            // Check if main app is accessible
            const localAppUrl = 'http://localhost:3000';
            const prodAppUrl = 'https://rag-prompt-library.web.app';
            
            log('Checking application accessibility...', 'info');
            
            // Test local app
            fetch(localAppUrl)
                .then(response => {
                    if (response.ok) {
                        log('✅ Local app is accessible', 'success');
                        updateStatus('environment-status', 'Local development server is running', 'success');
                    } else {
                        log('❌ Local app returned error: ' + response.status, 'error');
                    }
                })
                .catch(error => {
                    log('❌ Local app is not accessible: ' + error.message, 'error');
                    updateStatus('environment-status', 'Local development server is not running', 'error');
                });
            
            // Test production app
            fetch(prodAppUrl)
                .then(response => {
                    if (response.ok) {
                        log('✅ Production app is accessible', 'success');
                    } else {
                        log('❌ Production app returned error: ' + response.status, 'error');
                    }
                })
                .catch(error => {
                    log('❌ Production app is not accessible: ' + error.message, 'error');
                });
        }

        function testLocalApp() {
            log('🔍 Testing local application...', 'info');
            const localWindow = window.open('http://localhost:3000', '_blank');
            if (localWindow) {
                log('✅ Local app opened in new tab', 'success');
                log('💡 Check the browser console in the new tab for detailed logs', 'info');
            } else {
                log('❌ Failed to open local app (popup blocked?)', 'error');
            }
        }

        function testProductionApp() {
            log('🔍 Testing production application...', 'info');
            const prodWindow = window.open('https://rag-prompt-library.web.app', '_blank');
            if (prodWindow) {
                log('✅ Production app opened in new tab', 'success');
                log('💡 Test prompt creation in the production environment', 'info');
            } else {
                log('❌ Failed to open production app (popup blocked?)', 'error');
            }
        }

        function clearResults() {
            document.getElementById('debug-log').innerHTML = '<div>Debug log cleared...</div>';
            document.getElementById('test-results').innerHTML = '';
            updateStatus('environment-status', 'Environment check cleared', 'info');
        }

        // Initialize
        log('🔧 Comprehensive debug page loaded', 'info');
        log('📝 Use the buttons above to run diagnostics', 'info');
        
        // Auto-run quick diagnostics
        setTimeout(runQuickDiagnostics, 1000);
    </script>
</body>
</html>
