import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Settings } from '../pages/Settings';
import { useAuth } from '../contexts/AuthContext';
import { settingsService } from '../services/settingsService';

// Mock the auth context
vi.mock('../contexts/AuthContext');
const mockUseAuth = vi.mocked(useAuth);

// Mock the settings service
vi.mock('../services/settingsService');
const mockSettingsService = vi.mocked(settingsService);

// Mock the LoadingSpinner component
vi.mock('../components/common/LoadingSpinner', () => ({
  LoadingSpinner: ({ 'data-testid': dataTestId }: { 'data-testid'?: string }) => (
    <div data-testid={dataTestId || 'loading-spinner'}>Loading...</div>
  )
}));

// Mock the ApiKeyModal component
vi.mock('../components/settings/ApiKeyModal', () => ({
  ApiKeyModal: ({ isOpen, onClose, onSave }: any) => (
    isOpen ? (
      <div data-testid="api-key-modal">
        <button onClick={onClose} data-testid="modal-close">Close</button>
        <button 
          onClick={() => onSave({ name: 'Test Key', provider: 'OpenAI', key: 'sk-test123' })}
          data-testid="modal-save"
        >
          Save
        </button>
      </div>
    ) : null
  )
}));

const mockUser = {
  uid: 'test-user-id',
  email: '<EMAIL>',
  displayName: 'Test User'
};

const mockSettings = {
  profile: {
    displayName: 'Test User',
    email: '<EMAIL>',
    timezone: 'America/New_York',
    language: 'en'
  },
  apiKeys: [
    {
      id: 'key-1',
      name: 'Test OpenAI Key',
      provider: 'OpenAI',
      masked: 'sk-****123',
      lastUsed: '2024-01-01T00:00:00.000Z',
      isActive: true,
      createdAt: '2024-01-01T00:00:00.000Z'
    }
  ],
  notifications: {
    emailNotifications: true,
    promptSharing: true,
    systemUpdates: false,
    weeklyDigest: true,
    marketingEmails: false,
    securityAlerts: true
  },
  privacy: {
    profileVisibility: 'public' as const,
    allowAnalytics: true,
    shareUsageData: false,
    showOnlineStatus: true,
    allowDirectMessages: true
  },
  billing: {
    plan: 'free' as const,
    status: 'active' as const,
    currentPeriodEnd: '2024-02-01T00:00:00.000Z',
    paymentMethod: null
  }
};

describe('Settings Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    mockUseAuth.mockReturnValue({
      currentUser: mockUser,
      loading: false,
      signup: vi.fn(),
      login: vi.fn(),
      loginWithGoogle: vi.fn(),
      logout: vi.fn()
    });

    mockSettingsService.getOrCreateUserSettings.mockResolvedValue(mockSettings);
    mockSettingsService.validateSettings.mockReturnValue({ isValid: true, errors: [] });
    mockSettingsService.updateUserSettings.mockResolvedValue();
    mockSettingsService.maskApiKey.mockReturnValue('sk-****123');
  });

  it('renders loading state initially', async () => {
    render(<Settings />);
    
    expect(screen.getByTestId('settings-loading')).toBeInTheDocument();
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });

  it('renders settings after loading', async () => {
    render(<Settings />);
    
    await waitFor(() => {
      expect(screen.getByText('Settings')).toBeInTheDocument();
      expect(screen.getByText('Manage your account preferences and configuration')).toBeInTheDocument();
    });
  });

  it('renders all tabs', async () => {
    render(<Settings />);
    
    await waitFor(() => {
      expect(screen.getByText('Profile')).toBeInTheDocument();
      expect(screen.getByText('API Keys')).toBeInTheDocument();
      expect(screen.getByText('Notifications')).toBeInTheDocument();
      expect(screen.getByText('Privacy')).toBeInTheDocument();
      expect(screen.getByText('Billing')).toBeInTheDocument();
    });
  });

  it('switches between tabs', async () => {
    render(<Settings />);
    
    await waitFor(() => {
      expect(screen.getByText('Display Name')).toBeInTheDocument();
    });

    // Click on API Keys tab
    fireEvent.click(screen.getByText('API Keys'));
    expect(screen.getByText('Manage your AI provider API keys for prompt execution.')).toBeInTheDocument();

    // Click on Notifications tab
    fireEvent.click(screen.getByText('Notifications'));
    expect(screen.getByText('Choose what notifications you want to receive.')).toBeInTheDocument();
  });

  it('updates profile settings', async () => {
    render(<Settings />);
    
    await waitFor(() => {
      const displayNameInput = screen.getByDisplayValue('Test User');
      fireEvent.change(displayNameInput, { target: { value: 'Updated Name' } });
      expect(displayNameInput).toHaveValue('Updated Name');
    });
  });

  it('saves settings successfully', async () => {
    render(<Settings />);
    
    await waitFor(() => {
      const saveButton = screen.getByText('Save Changes');
      fireEvent.click(saveButton);
    });

    await waitFor(() => {
      expect(mockSettingsService.updateUserSettings).toHaveBeenCalledWith(
        mockUser.uid,
        expect.objectContaining({
          profile: expect.objectContaining({
            displayName: 'Test User'
          })
        })
      );
    });
  });

  it('displays validation errors', async () => {
    mockSettingsService.validateSettings.mockReturnValue({
      isValid: false,
      errors: ['Display name is required', 'Invalid timezone']
    });

    render(<Settings />);
    
    await waitFor(() => {
      const saveButton = screen.getByText('Save Changes');
      fireEvent.click(saveButton);
    });

    await waitFor(() => {
      expect(screen.getByText('Display name is required')).toBeInTheDocument();
      expect(screen.getByText('Invalid timezone')).toBeInTheDocument();
    });
  });

  it('opens API key modal', async () => {
    render(<Settings />);
    
    await waitFor(() => {
      fireEvent.click(screen.getByText('API Keys'));
    });

    const addButton = screen.getByText('Add API Key');
    fireEvent.click(addButton);

    expect(screen.getByTestId('api-key-modal')).toBeInTheDocument();
  });

  it('adds new API key', async () => {
    mockSettingsService.addApiKey.mockResolvedValue('new-key-id');

    render(<Settings />);
    
    await waitFor(() => {
      fireEvent.click(screen.getByText('API Keys'));
    });

    fireEvent.click(screen.getByText('Add API Key'));
    fireEvent.click(screen.getByTestId('modal-save'));

    await waitFor(() => {
      expect(mockSettingsService.addApiKey).toHaveBeenCalledWith(
        mockUser.uid,
        { name: 'Test Key', provider: 'OpenAI', key: 'sk-test123' }
      );
    });
  });

  it('removes API key', async () => {
    mockSettingsService.removeApiKey.mockResolvedValue();

    render(<Settings />);
    
    await waitFor(() => {
      fireEvent.click(screen.getByText('API Keys'));
    });

    const deleteButton = screen.getByText('Delete');
    fireEvent.click(deleteButton);

    await waitFor(() => {
      expect(mockSettingsService.removeApiKey).toHaveBeenCalledWith(
        mockUser.uid,
        'key-1'
      );
    });
  });

  it('toggles notification settings', async () => {
    render(<Settings />);
    
    await waitFor(() => {
      fireEvent.click(screen.getByText('Notifications'));
    });

    // Find toggle switches and click one
    const toggles = screen.getAllByRole('button');
    const emailToggle = toggles.find(toggle => 
      toggle.className.includes('bg-indigo-600')
    );
    
    if (emailToggle) {
      fireEvent.click(emailToggle);
      // Verify the state change would be reflected in the settings
    }
  });

  it('handles loading error', async () => {
    mockSettingsService.getOrCreateUserSettings.mockRejectedValue(new Error('Failed to load'));

    render(<Settings />);
    
    await waitFor(() => {
      expect(screen.getByText('Failed to load settings. Please try refreshing the page.')).toBeInTheDocument();
    });
  });

  it('handles save error', async () => {
    mockSettingsService.updateUserSettings.mockRejectedValue(new Error('Save failed'));

    render(<Settings />);
    
    await waitFor(() => {
      const saveButton = screen.getByText('Save Changes');
      fireEvent.click(saveButton);
    });

    await waitFor(() => {
      expect(screen.getByText('Failed to save settings. Please try again.')).toBeInTheDocument();
    });
  });
});
