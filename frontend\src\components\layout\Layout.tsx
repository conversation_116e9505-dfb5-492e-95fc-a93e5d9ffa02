import React, { useState, useEffect } from 'react';
import { Outlet } from 'react-router-dom';
import { Header } from './Header';
import { Sidebar } from './Sidebar';
import FeedbackWidget from '../beta/FeedbackWidget';
import { GuidedOnboarding } from '../help/GuidedOnboarding';
import { useAuth } from '../../contexts/AuthContext';
import { useUserProfile } from '../../contexts/UserProfileContext';

export const Layout: React.FC = () => {
  const { currentUser } = useAuth();
  const { userProfile, loading: profileLoading } = useUserProfile();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [isFirstTimeUser, setIsFirstTimeUser] = useState(false);

  // Check if user is first-time user and should see onboarding
  useEffect(() => {
    if (!currentUser || profileLoading) return;

    const checkFirstTimeUser = () => {
      const storageKey = `onboardingData_${currentUser.uid}`;
      const savedData = localStorage.getItem(storageKey);

      // Check if this is a newly created user profile (created within last 5 minutes)
      const isNewProfile = userProfile && userProfile.createdAt &&
        (new Date().getTime() - new Date(userProfile.createdAt).getTime()) < 5 * 60 * 1000;

      if (!savedData) {
        // No onboarding data found - this is a first-time user
        setIsFirstTimeUser(true);
        setShowOnboarding(true);
      } else {
        const parsed = JSON.parse(savedData);
        // Show onboarding if not completed and not skipped
        if (!parsed.skippedOnboarding && parsed.completedSteps.length < 3) { // 3 required steps
          setShowOnboarding(true);
        }
      }

      // Also show onboarding for newly created profiles regardless of localStorage
      if (isNewProfile && !savedData) {
        setIsFirstTimeUser(true);
        setShowOnboarding(true);
      }
    };

    // Small delay to ensure the user profile has fully loaded
    const timer = setTimeout(checkFirstTimeUser, 1000);
    return () => clearTimeout(timer);
  }, [currentUser, userProfile, profileLoading]);

  const handleOnboardingComplete = () => {
    setShowOnboarding(false);
  };

  return (
    <div className="h-screen flex overflow-hidden bg-gray-100 dark:bg-gray-900">
      <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />
      
      <div className="flex flex-col w-0 flex-1 overflow-hidden">
        <Header onMenuToggle={() => setSidebarOpen(true)} />
        
        <main className="flex-1 relative overflow-y-auto focus:outline-none">
          <div className="py-6">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <Outlet />
            </div>
          </div>
        </main>
      </div>

      {/* Beta Feedback Widget */}
      <FeedbackWidget />

      {/* Guided Onboarding - appears after login for first-time users */}
      {showOnboarding && (
        <GuidedOnboarding
          isFirstTimeUser={isFirstTimeUser}
          onComplete={handleOnboardingComplete}
        />
      )}
    </div>
  );
};
