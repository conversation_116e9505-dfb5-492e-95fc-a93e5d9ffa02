import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { FileText, Database, Play, TrendingUp, RefreshCw, AlertCircle } from 'lucide-react';
import { useDashboardData } from '../hooks/useDashboardData';

export const Dashboard: React.FC = () => {
  const { userProfile } = useAuth();
  const navigate = useNavigate();
  const {
    stats,
    recentActivity,
    loading,
    error,
    refresh,
    clearCache,
    formatNumber,
    formatPercentage,
    formatCurrency,
    formatDuration,
    getChangeIndicator,
    getEmptyStateMessage,
    lastUpdated,
    hasData
  } = useDashboardData();

  // Navigation handlers
  const handleCreatePrompt = () => {
    navigate('/prompts');
  };

  const handleUploadDocument = () => {
    navigate('/documents');
  };

  // Enhanced refresh handler with user feedback
  const handleRefresh = async () => {
    try {
      await refresh();
      // Could add a toast notification here for success feedback
    } catch (error) {
      console.error('Failed to refresh dashboard:', error);
      // Could add a toast notification here for error feedback
    }
  };

  // Transform stats data for display
  const statsDisplay = [
    {
      name: 'Total Prompts',
      value: formatNumber(stats.totalPrompts),
      icon: FileText,
      ...getChangeIndicator(stats.totalPrompts, 'positive')
    },
    {
      name: 'Documents',
      value: formatNumber(stats.totalDocuments),
      icon: Database,
      ...getChangeIndicator(stats.totalDocuments, 'positive')
    },
    {
      name: 'Executions',
      value: formatNumber(stats.totalExecutions),
      icon: Play,
      ...getChangeIndicator(stats.totalExecutions, 'positive')
    },
    {
      name: 'Success Rate',
      value: formatPercentage(stats.successRate),
      icon: TrendingUp,
      ...getChangeIndicator(stats.successRate, 'positive')
    }
  ];

  // Loading state
  if (loading && !lastUpdated) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading dashboard data...</p>
        </div>
      </div>
    );
  }

  // Empty state when no data is available
  if (!loading && !error && !hasData) {
    const emptyState = getEmptyStateMessage();
    return (
      <div>
        {/* Welcome section */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Welcome back, {userProfile?.displayName || 'User'}!
          </h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Let's get you started with the RAG Prompt Library.
          </p>
        </div>

        {/* Empty state */}
        <div className="text-center py-12">
          <FileText className="mx-auto h-16 w-16 text-gray-400" />
          <h3 className="mt-4 text-lg font-medium text-gray-900 dark:text-white">{emptyState.title}</h3>
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400 max-w-md mx-auto">
            {emptyState.description}
          </p>
          <div className="mt-6 flex justify-center space-x-4">
            <button
              onClick={handleCreatePrompt}
              className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              Create Your First Prompt
            </button>
            <button
              onClick={handleUploadDocument}
              className="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
            >
              Upload Documents
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Welcome section */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Welcome back, {userProfile?.displayName || 'User'}!
            </h1>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              Here's what's happening with your prompts today.
            </p>
          </div>
          <div className="flex items-center space-x-4">
            {lastUpdated && (
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Last updated: {lastUpdated.toLocaleTimeString()}
              </p>
            )}
            <button
              onClick={handleRefresh}
              disabled={loading}
              className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              title={loading ? 'Refreshing...' : 'Refresh dashboard data'}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              {loading ? 'Refreshing...' : 'Refresh'}
            </button>
          </div>
        </div>

        {/* Error state */}
        {error && (
          <div className="mt-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
            <div className="flex">
              <AlertCircle className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                  Error loading dashboard data
                </h3>
                <p className="mt-1 text-sm text-red-700 dark:text-red-300">{error}</p>
                <button
                  onClick={handleRefresh}
                  disabled={loading}
                  className="mt-2 text-sm text-red-600 dark:text-red-400 hover:text-red-500 dark:hover:text-red-300 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:underline"
                >
                  {loading ? 'Retrying...' : 'Try again'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Stats grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        {statsDisplay.map((stat) => (
          <div
            key={stat.name}
            className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"
          >
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <stat.icon className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                      {stat.name}
                    </dt>
                    <dd className="text-lg font-medium text-gray-900 dark:text-white">
                      {stat.value}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
            <div className="bg-gray-50 dark:bg-gray-700 px-5 py-3">
              <div className="text-sm">
                <span className={`font-medium ${
                  stat.changeType === 'positive'
                    ? 'text-green-600 dark:text-green-400'
                    : stat.changeType === 'negative'
                    ? 'text-red-600 dark:text-red-400'
                    : 'text-gray-600 dark:text-gray-400'
                }`}>
                  {stat.change}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Recent activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent activity */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">
              Recent Activity
            </h3>
            <div className="mt-5">
              <div className="flow-root">
                {recentActivity.length > 0 ? (
                  <ul className="-my-5 divide-y divide-gray-200 dark:divide-gray-700">
                    {recentActivity.slice(0, 5).map((activity) => (
                      <li key={activity.id} className="py-4">
                        <div className="flex items-center space-x-4">
                          <div className="flex-shrink-0">
                            {activity.type === 'execution_run' && <Play className="h-8 w-8 text-blue-400" />}
                            {activity.type === 'prompt_created' && <FileText className="h-8 w-8 text-green-400" />}
                            {activity.type === 'document_uploaded' && <Database className="h-8 w-8 text-purple-400" />}
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                              {activity.title}
                            </p>
                            <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                              <span>{activity.timestamp.toLocaleString()}</span>
                              {activity.cost && (
                                <span>• {formatCurrency(activity.cost)}</span>
                              )}
                              {activity.duration && (
                                <span>• {formatDuration(activity.duration)}</span>
                              )}
                            </div>
                          </div>
                          <div>
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              activity.type === 'execution_run'
                                ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                                : activity.type === 'prompt_created'
                                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                : 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
                            }`}>
                              {activity.type === 'execution_run' ? 'Executed' :
                               activity.type === 'prompt_created' ? 'Created' : 'Uploaded'}
                            </span>
                          </div>
                        </div>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <div className="text-center py-8">
                    <FileText className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No recent activity</h3>
                    <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                      Start by creating a prompt or uploading a document.
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Performance Summary */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">
              Performance Summary
            </h3>
            <div className="mt-5 space-y-4">
              {/* Cost Summary */}
              <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">Total Cost</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {formatCurrency(stats.totalCost)}
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-500 dark:text-gray-400">Avg per execution</p>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {stats.totalExecutions > 0
                      ? formatCurrency(stats.totalCost / stats.totalExecutions)
                      : formatCurrency(0)
                    }
                  </p>
                </div>
              </div>

              {/* Performance Metrics */}
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <p className="text-sm text-blue-600 dark:text-blue-400">Avg Response Time</p>
                  <p className="text-lg font-semibold text-blue-900 dark:text-blue-100">
                    {formatDuration(stats.avgExecutionTime)}
                  </p>
                </div>
                <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <p className="text-sm text-green-600 dark:text-green-400">Success Rate</p>
                  <p className="text-lg font-semibold text-green-900 dark:text-green-100">
                    {formatPercentage(stats.successRate)}
                  </p>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="pt-4 border-t border-gray-200 dark:border-gray-600">
                <div className="flex space-x-3">
                  <button
                    onClick={handleCreatePrompt}
                    className="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-4 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  >
                    Create Prompt
                  </button>
                  <button
                    onClick={handleUploadDocument}
                    className="flex-1 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-4 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                  >
                    Upload Document
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
