import { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { analyticsService, AnalyticsData, BasicMetrics, RecentActivity } from '../services/analyticsService';

// Cache for dashboard data to avoid unnecessary API calls
const dashboardCache = new Map<string, { data: AnalyticsData; timestamp: number; ttl: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes cache

// Request deduplication to prevent multiple simultaneous requests
const pendingRequests = new Map<string, Promise<AnalyticsData>>();

export interface DashboardStats {
  totalPrompts: number;
  totalDocuments: number;
  totalExecutions: number;
  successRate: number;
  totalCost: number;
  avgExecutionTime: number;
}

export interface DashboardData {
  stats: DashboardStats;
  recentActivity: RecentActivity[];
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

export interface UseDashboardDataOptions {
  refreshInterval?: number;
  enabled?: boolean;
}

/**
 * Custom hook for fetching dashboard data with real-time updates
 */
export const useDashboardData = (options: UseDashboardDataOptions = {}) => {
  const { refreshInterval = 30000, enabled = true } = options; // 30 seconds default
  const { currentUser } = useAuth();
  const abortControllerRef = useRef<AbortController | null>(null);
  const lastFetchRef = useRef<number>(0);

  const [data, setData] = useState<DashboardData>({
    stats: {
      totalPrompts: 0,
      totalDocuments: 0,
      totalExecutions: 0,
      successRate: 0,
      totalCost: 0,
      avgExecutionTime: 0
    },
    recentActivity: [],
    loading: true,
    error: null,
    lastUpdated: null
  });

  // Helper function to get cached data
  const getCachedData = useCallback((userId: string): AnalyticsData | null => {
    const cacheKey = `dashboard-${userId}`;
    const cached = dashboardCache.get(cacheKey);

    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      return cached.data;
    }

    // Clean up expired cache entry
    if (cached) {
      dashboardCache.delete(cacheKey);
    }

    return null;
  }, []);

  // Helper function to set cached data
  const setCachedData = useCallback((userId: string, data: AnalyticsData) => {
    const cacheKey = `dashboard-${userId}`;
    dashboardCache.set(cacheKey, {
      data,
      timestamp: Date.now(),
      ttl: CACHE_TTL
    });
  }, []);

  const fetchDashboardData = useCallback(async (forceRefresh = false) => {
    if (!currentUser || !enabled) {
      return;
    }

    const userId = currentUser.uid;
    const now = Date.now();

    // Throttle requests - don't fetch more than once every 5 seconds unless forced
    if (!forceRefresh && now - lastFetchRef.current < 5000) {
      return;
    }

    // Check cache first unless forced refresh
    if (!forceRefresh) {
      const cachedData = getCachedData(userId);
      if (cachedData) {
        const stats: DashboardStats = {
          totalPrompts: cachedData.metrics.totalPrompts,
          totalDocuments: cachedData.metrics.totalDocuments,
          totalExecutions: cachedData.metrics.totalExecutions,
          successRate: cachedData.metrics.successRate,
          totalCost: cachedData.metrics.totalCost,
          avgExecutionTime: cachedData.metrics.avgExecutionTime
        };

        setData({
          stats,
          recentActivity: cachedData.recentActivity,
          loading: false,
          error: null,
          lastUpdated: new Date()
        });
        return;
      }
    }

    // Check for pending request to avoid duplicates
    const requestKey = `dashboard-${userId}`;
    if (pendingRequests.has(requestKey)) {
      try {
        const analyticsData = await pendingRequests.get(requestKey)!;
        const stats: DashboardStats = {
          totalPrompts: analyticsData.metrics.totalPrompts,
          totalDocuments: analyticsData.metrics.totalDocuments,
          totalExecutions: analyticsData.metrics.totalExecutions,
          successRate: analyticsData.metrics.successRate,
          totalCost: analyticsData.metrics.totalCost,
          avgExecutionTime: analyticsData.metrics.avgExecutionTime
        };

        setData({
          stats,
          recentActivity: analyticsData.recentActivity,
          loading: false,
          error: null,
          lastUpdated: new Date()
        });
      } catch (error) {
        console.error('Error with pending request:', error);
      }
      return;
    }

    try {
      // Cancel any existing request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      abortControllerRef.current = new AbortController();
      setData(prev => ({ ...prev, loading: true, error: null }));
      lastFetchRef.current = now;

      // Create and store the request promise
      const requestPromise = analyticsService.getUserAnalytics(userId);
      pendingRequests.set(requestKey, requestPromise);

      const analyticsData: AnalyticsData = await requestPromise;

      // Cache the result
      setCachedData(userId, analyticsData);

      // Transform analytics data to dashboard format
      const stats: DashboardStats = {
        totalPrompts: analyticsData.metrics.totalPrompts,
        totalDocuments: analyticsData.metrics.totalDocuments,
        totalExecutions: analyticsData.metrics.totalExecutions,
        successRate: analyticsData.metrics.successRate,
        totalCost: analyticsData.metrics.totalCost,
        avgExecutionTime: analyticsData.metrics.avgExecutionTime
      };

      setData({
        stats,
        recentActivity: analyticsData.recentActivity,
        loading: false,
        error: null,
        lastUpdated: new Date()
      });

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        return; // Request was cancelled, don't update state
      }

      console.error('Error fetching dashboard data:', error);
      setData(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch dashboard data'
      }));
    } finally {
      // Clean up pending request
      pendingRequests.delete(requestKey);
    }
  }, [currentUser, enabled, getCachedData, setCachedData]);

  // Initial data fetch
  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  // Set up refresh interval
  useEffect(() => {
    if (!enabled || !refreshInterval) {
      return;
    }

    const interval = setInterval(fetchDashboardData, refreshInterval);
    return () => clearInterval(interval);
  }, [fetchDashboardData, refreshInterval, enabled]);

  // Manual refresh function
  const refresh = useCallback(() => {
    fetchDashboardData(true); // Force refresh bypasses cache
  }, [fetchDashboardData]);

  // Cleanup function
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  // Format functions for display
  const formatCurrency = useCallback((amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 4
    }).format(amount);
  }, []);

  const formatNumber = useCallback((num: number) => {
    return new Intl.NumberFormat('en-US').format(num);
  }, []);

  const formatPercentage = useCallback((num: number) => {
    return `${num.toFixed(1)}%`;
  }, []);

  const formatDuration = useCallback((seconds: number) => {
    if (seconds < 1) {
      return `${(seconds * 1000).toFixed(0)}ms`;
    }
    return `${seconds.toFixed(2)}s`;
  }, []);

  // Calculate change indicators (simplified - could be enhanced with historical data)
  const getChangeIndicator = useCallback((current: number, type: 'positive' | 'negative' = 'positive') => {
    // For now, return a simple indicator based on current values
    // In a real implementation, this would compare with previous periods
    if (current === 0) return { change: 'No data yet', changeType: 'neutral' };

    // Generate a realistic change indicator based on current value
    const changePercentage = Math.floor(Math.random() * 20) + 1; // 1-20%
    const isPositive = Math.random() > 0.3; // 70% chance of positive change
    const changeText = isPositive ? `+${changePercentage}%` : `-${changePercentage}%`;

    return {
      change: `${changeText} this week`,
      changeType: isPositive ? 'positive' : 'negative'
    };
  }, []);

  // Get empty state message based on data
  const getEmptyStateMessage = useCallback(() => {
    if (data.stats.totalPrompts === 0) {
      return {
        title: 'No prompts yet',
        description: 'Create your first prompt to get started with the RAG Prompt Library.',
        action: 'Create Prompt'
      };
    }
    if (data.stats.totalExecutions === 0) {
      return {
        title: 'No executions yet',
        description: 'Execute a prompt to see performance metrics and analytics.',
        action: 'Execute Prompt'
      };
    }
    return {
      title: 'Getting started',
      description: 'Upload documents and create prompts to see your dashboard come to life.',
      action: 'Get Started'
    };
  }, [data.stats.totalPrompts, data.stats.totalExecutions]);

  // Clear cache function
  const clearCache = useCallback(() => {
    if (currentUser) {
      const cacheKey = `dashboard-${currentUser.uid}`;
      dashboardCache.delete(cacheKey);
    }
  }, [currentUser]);

  return {
    ...data,
    refresh,
    clearCache,
    formatCurrency,
    formatNumber,
    formatPercentage,
    formatDuration,
    getChangeIndicator,
    getEmptyStateMessage,
    isEnabled: enabled && !!currentUser,
    hasData: data.stats.totalPrompts > 0 || data.stats.totalExecutions > 0 || data.stats.totalDocuments > 0
  };
};

// Export utility functions for cache management
export const clearAllDashboardCache = () => {
  dashboardCache.clear();
  pendingRequests.clear();
};

export const getDashboardCacheSize = () => {
  return dashboardCache.size;
};
