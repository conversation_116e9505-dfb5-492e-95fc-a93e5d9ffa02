// Test database connectivity for RAG Prompt Library
// This script tests the connection to Firestore and verifies CRUD operations

import { initializeApp } from 'firebase/app';
import { 
  getAuth, 
  signInAnonymously, 
  onAuthStateChanged,
  signOut 
} from 'firebase/auth';
import { 
  getFirestore, 
  collection, 
  addDoc, 
  getDocs, 
  doc,
  getDoc,
  updateDoc,
  deleteDoc,
  serverTimestamp,
  query,
  where,
  orderBy,
  limit
} from 'firebase/firestore';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDJWjw2e8FayU3CvIWyGXXFAqDCTFN5CJs",
  authDomain: "rag-prompt-library.firebaseapp.com",
  projectId: "rag-prompt-library",
  storageBucket: "rag-prompt-library.firebasestorage.app",
  messagingSenderId: "743998930129",
  appId: "1:743998930129:web:69dd61394ed81598cd99f0",
  measurementId: "G-CEDFF0WMPW"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

// Test configuration
const TEST_CONFIG = {
  testPrompt: {
    title: 'Database Connectivity Test Prompt',
    content: 'This is a test prompt to verify database connectivity and CRUD operations.',
    description: 'Test prompt for database connectivity verification',
    category: 'General',
    tags: ['test', 'database', 'connectivity'],
    isPublic: false,
    variables: []
  },
  maxRetries: 3,
  retryDelay: 1000
};

// Utility functions
const log = (level, message, data = null) => {
  const timestamp = new Date().toISOString();
  const prefix = `[${timestamp}] [${level.toUpperCase()}]`;
  
  if (data) {
    console.log(`${prefix} ${message}`, data);
  } else {
    console.log(`${prefix} ${message}`);
  }
};

const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Authentication test
export const testAuthentication = async () => {
  log('info', '🔐 Testing authentication...');
  
  return new Promise((resolve, reject) => {
    const timeout = setTimeout(() => {
      reject(new Error('Authentication timeout'));
    }, 10000);

    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      clearTimeout(timeout);
      unsubscribe();
      
      if (user) {
        log('info', '✅ User already authenticated', { uid: user.uid });
        resolve(user);
      } else {
        try {
          log('info', '🔄 Attempting anonymous sign-in...');
          const result = await signInAnonymously(auth);
          log('info', '✅ Anonymous authentication successful', { uid: result.user.uid });
          resolve(result.user);
        } catch (error) {
          log('error', '❌ Authentication failed', error);
          reject(error);
        }
      }
    });
  });
};

// Database connection test
export const testDatabaseConnection = async () => {
  log('info', '🔗 Testing database connection...');
  
  try {
    // Test basic connection by attempting to read from a system collection
    const testRef = collection(db, 'test');
    log('info', '✅ Database connection established');
    return true;
  } catch (error) {
    log('error', '❌ Database connection failed', error);
    throw error;
  }
};

// CRUD operations test
export const testCRUDOperations = async (user) => {
  if (!user) {
    throw new Error('User authentication required for CRUD operations');
  }

  log('info', '🔄 Testing CRUD operations...');
  const userId = user.uid;
  let createdPromptId = null;

  try {
    // CREATE operation
    log('info', '➕ Testing CREATE operation...');
    const promptsRef = collection(db, 'users', userId, 'prompts');
    
    const newPrompt = {
      ...TEST_CONFIG.testPrompt,
      createdBy: userId,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      version: 1
    };

    const docRef = await addDoc(promptsRef, newPrompt);
    createdPromptId = docRef.id;
    log('info', '✅ CREATE operation successful', { id: createdPromptId });

    // READ operation
    log('info', '📖 Testing READ operation...');
    const createdDoc = await getDoc(docRef);
    
    if (createdDoc.exists()) {
      log('info', '✅ READ operation successful', { data: createdDoc.data() });
    } else {
      throw new Error('Created document not found');
    }

    // LIST operation
    log('info', '📋 Testing LIST operation...');
    const querySnapshot = await getDocs(promptsRef);
    log('info', '✅ LIST operation successful', { count: querySnapshot.size });

    // UPDATE operation
    log('info', '✏️ Testing UPDATE operation...');
    const updateData = {
      title: 'Updated Test Prompt',
      updatedAt: serverTimestamp(),
      version: 2
    };
    
    await updateDoc(docRef, updateData);
    
    // Verify update
    const updatedDoc = await getDoc(docRef);
    if (updatedDoc.exists() && updatedDoc.data().title === 'Updated Test Prompt') {
      log('info', '✅ UPDATE operation successful');
    } else {
      throw new Error('Update verification failed');
    }

    // QUERY operation
    log('info', '🔍 Testing QUERY operation...');
    const q = query(
      promptsRef,
      where('createdBy', '==', userId),
      orderBy('updatedAt', 'desc'),
      limit(10)
    );
    
    const queryResult = await getDocs(q);
    log('info', '✅ QUERY operation successful', { count: queryResult.size });

    // DELETE operation
    log('info', '🗑️ Testing DELETE operation...');
    await deleteDoc(docRef);
    
    // Verify deletion
    const deletedDoc = await getDoc(docRef);
    if (!deletedDoc.exists()) {
      log('info', '✅ DELETE operation successful');
    } else {
      throw new Error('Delete verification failed');
    }

    log('info', '🎉 All CRUD operations completed successfully!');
    return true;

  } catch (error) {
    log('error', '❌ CRUD operations failed', error);
    
    // Cleanup: try to delete the test document if it was created
    if (createdPromptId) {
      try {
        const docRef = doc(db, 'users', userId, 'prompts', createdPromptId);
        await deleteDoc(docRef);
        log('info', '🧹 Cleanup: Test document deleted');
      } catch (cleanupError) {
        log('warning', '⚠️ Cleanup failed', cleanupError);
      }
    }
    
    throw error;
  }
};

// Performance test
export const testPerformance = async (user) => {
  if (!user) {
    throw new Error('User authentication required for performance test');
  }

  log('info', '⚡ Testing database performance...');
  const userId = user.uid;
  const promptsRef = collection(db, 'users', userId, 'prompts');
  
  const operations = [];
  const startTime = Date.now();

  try {
    // Test multiple concurrent operations
    for (let i = 0; i < 5; i++) {
      const testPrompt = {
        ...TEST_CONFIG.testPrompt,
        title: `Performance Test Prompt ${i + 1}`,
        createdBy: userId,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        version: 1
      };

      operations.push(addDoc(promptsRef, testPrompt));
    }

    const results = await Promise.all(operations);
    const endTime = Date.now();
    const duration = endTime - startTime;

    log('info', '✅ Performance test completed', {
      operations: results.length,
      duration: `${duration}ms`,
      avgPerOperation: `${(duration / results.length).toFixed(2)}ms`
    });

    // Cleanup performance test documents
    const cleanupOperations = results.map(docRef => deleteDoc(docRef));
    await Promise.all(cleanupOperations);
    log('info', '🧹 Performance test cleanup completed');

    return {
      operationsCount: results.length,
      totalDuration: duration,
      averageDuration: duration / results.length
    };

  } catch (error) {
    log('error', '❌ Performance test failed', error);
    throw error;
  }
};

// Comprehensive database connectivity test
export const runDatabaseConnectivityTest = async () => {
  log('info', '🚀 Starting comprehensive database connectivity test...');
  
  try {
    // Step 1: Test authentication
    const user = await testAuthentication();
    
    // Step 2: Test basic database connection
    await testDatabaseConnection();
    
    // Step 3: Test CRUD operations
    await testCRUDOperations(user);
    
    // Step 4: Test performance
    const perfResults = await testPerformance(user);
    
    // Step 5: Cleanup - sign out
    await signOut(auth);
    log('info', '🔓 User signed out');
    
    log('info', '🎉 Database connectivity test completed successfully!');
    
    return {
      success: true,
      performance: perfResults,
      timestamp: new Date().toISOString()
    };
    
  } catch (error) {
    log('error', '❌ Database connectivity test failed', error);
    
    // Attempt cleanup
    try {
      await signOut(auth);
    } catch (signOutError) {
      log('warning', '⚠️ Sign out during cleanup failed', signOutError);
    }
    
    return {
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
};

// Make functions available globally for browser testing
if (typeof window !== 'undefined') {
  window.databaseConnectivityTest = {
    testAuthentication,
    testDatabaseConnection,
    testCRUDOperations,
    testPerformance,
    runDatabaseConnectivityTest
  };
  
  log('info', '🧪 Database connectivity test functions available at window.databaseConnectivityTest');
}
