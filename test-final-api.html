<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final API Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; background: #f5f5f5; }
        .container { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin: 20px 0; }
        .header { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 20px; border-radius: 8px; text-align: center; }
        .log { background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; margin: 10px 0; font-family: monospace; font-size: 12px; white-space: pre-wrap; max-height: 300px; overflow-y: auto; }
        button { padding: 12px 24px; margin: 8px; border: none; border-radius: 6px; cursor: pointer; font-weight: bold; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 15px; border-radius: 8px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 Final API Test</h1>
        <p>Testing the updated routing with enhanced logging</p>
    </div>

    <div class="container">
        <h3>🌐 API Endpoint Tests</h3>
        <button class="btn-primary" onclick="testDocumentStatus()">Test Document Status</button>
        <button class="btn-success" onclick="testHealth()">Test Health</button>
        <button class="btn-info" onclick="testWithRealDocument()">Test with Real Document ID</button>
        
        <div id="testLog" class="log">Ready to test the updated API routing...</div>
    </div>

    <div class="container">
        <h3>📊 Expected Results</h3>
        <div class="success">
            <strong>✅ Success Indicators:</strong>
            <ul>
                <li>Document Status: Returns JSON with "Document not found" (404) for test IDs</li>
                <li>Health Check: Returns JSON with "status": "healthy"</li>
                <li>Content-Type: application/json</li>
                <li>No HTML responses</li>
            </ul>
        </div>
        
        <div class="warning">
            <strong>⚠️ If Still Not Working:</strong>
            <ul>
                <li>Check Firebase Functions logs for routing details</li>
                <li>Verify Firebase hosting rewrite rules</li>
                <li>Test direct function URL</li>
            </ul>
        </div>
    </div>

    <script>
        function log(message) {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        async function testDocumentStatus() {
            log('🔍 Testing document status endpoint...');
            
            try {
                const response = await fetch('https://rag-prompt-library.web.app/api/ai/document-status/test-job-123');
                
                log(`📊 Status: ${response.status} ${response.statusText}`);
                log(`📋 Content-Type: ${response.headers.get('content-type')}`);
                
                const data = await response.json();
                log(`📄 Response: ${JSON.stringify(data, null, 2)}`);
                
                if (data.success === false && data.error && data.error.includes('Document not found')) {
                    log('✅ SUCCESS: Document status endpoint working correctly!');
                    log('   (404 "Document not found" is expected for test job ID)');
                    showSuccess('Document Status API is working correctly!');
                } else if (data.success !== undefined) {
                    log('✅ SUCCESS: API responding with proper JSON structure!');
                    showSuccess('API structure is correct!');
                } else if (data.error && data.error.includes('Unknown API endpoint')) {
                    log('❌ ISSUE: Routing still not working - endpoint not recognized');
                    showError('API routing needs more work');
                } else {
                    log('⚠️ Unexpected response structure');
                }
                
            } catch (error) {
                log(`❌ Error: ${error.message}`);
                if (error.message.includes('Unexpected token')) {
                    showError('Still getting HTML responses - routing not fixed');
                }
            }
        }

        async function testHealth() {
            log('🏥 Testing health endpoint...');
            
            try {
                const response = await fetch('https://rag-prompt-library.web.app/api/health');
                const data = await response.json();
                
                log(`📊 Status: ${response.status}`);
                log(`📄 Response: ${JSON.stringify(data, null, 2)}`);
                
                if (data.status === 'healthy') {
                    log('✅ SUCCESS: Health endpoint working perfectly!');
                    showSuccess('Health API is working!');
                } else if (data.error && data.error.includes('Unknown API endpoint')) {
                    log('❌ ISSUE: Health endpoint routing not working');
                } else {
                    log('⚠️ Unexpected health response');
                }
                
            } catch (error) {
                log(`❌ Health endpoint error: ${error.message}`);
            }
        }

        async function testWithRealDocument() {
            log('📄 Testing with a potentially real document ID...');
            
            // Try a few different document ID formats that might exist
            const testIds = [
                'doc_123',
                'document_456',
                'test-document-789'
            ];
            
            for (const docId of testIds) {
                try {
                    log(`🔍 Testing with ID: ${docId}`);
                    const response = await fetch(`https://rag-prompt-library.web.app/api/ai/document-status/${docId}`);
                    const data = await response.json();
                    
                    log(`   Status: ${response.status}`);
                    log(`   Response: ${JSON.stringify(data, null, 2)}`);
                    
                    if (data.success === false && data.error === 'Document not found') {
                        log('   ✅ Endpoint working (document not found is expected)');
                    } else if (data.success === true) {
                        log('   🎉 Found a real document! API fully functional!');
                        showSuccess('Found real document - API fully working!');
                        break;
                    }
                    
                } catch (error) {
                    log(`   ❌ Error with ${docId}: ${error.message}`);
                }
            }
        }

        function showSuccess(message) {
            const container = document.querySelector('.container:last-child');
            container.insertAdjacentHTML('beforeend', `<div class="success">🎉 ${message}</div>`);
        }

        function showError(message) {
            const container = document.querySelector('.container:last-child');
            container.insertAdjacentHTML('beforeend', `<div class="error">❌ ${message}</div>`);
        }

        // Auto-test on load
        setTimeout(() => {
            log('🚀 Starting automatic API tests...');
            testDocumentStatus();
        }, 1000);
    </script>
</body>
</html>
