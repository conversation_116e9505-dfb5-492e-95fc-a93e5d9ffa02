import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import type { Prompt, PromptVariable, PromptExecution } from '../../types';
import { Play, Settings, Clock, DollarSign, Zap, Brain, FileText, Search, AlertCircle } from 'lucide-react';
import { Button } from '../common/Button';
import { LoadingSpinner } from '../common/LoadingSpinner';
import { httpsCallable } from 'firebase/functions';
import { functions } from '../../config/firebase';
import { DocumentService } from '../../services/documentService';
import type { RAGDocument } from '../../types';
import ModelSelector from './ModelSelector';
import ModelComparison from './ModelComparison';

interface PromptExecutorProps {
  prompt: Prompt;
  onExecutionComplete?: (execution: PromptExecution) => void;
}

interface ExecutionSettings {
  useRAG: boolean;
  models: string[];
  useMultiModel: boolean;
  temperature: number;
  maxTokens: number;
  topP: number;
}

export const PromptExecutor: React.FC<PromptExecutorProps> = ({
  prompt,
  onExecutionComplete
}) => {
  const { currentUser } = useAuth();
  const [variables, setVariables] = useState<Record<string, string | number | boolean | string[]>>({});
  const [settings, setSettings] = useState<ExecutionSettings>({
    useRAG: false,
    models: ['meta-llama/llama-3.2-11b-vision-instruct:free'],
    useMultiModel: false,
    temperature: 0.7,
    maxTokens: 1000,
    topP: 1.0
  });
  const [showSettings, setShowSettings] = useState(false);
  const [executing, setExecuting] = useState(false);
  const [result, setResult] = useState<PromptExecution | null>(null);
  const [multiModelResult, setMultiModelResult] = useState<any>(null);
  const [testingConnection, setTestingConnection] = useState(false);
  const [documents, setDocuments] = useState<RAGDocument[]>([]);
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);
  const [loadingDocuments, setLoadingDocuments] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [executionError, setExecutionError] = useState<string | null>(null);



  // Load user documents for RAG
  const loadUserDocuments = useCallback(async () => {
    if (!currentUser) return;

    try {
      setLoadingDocuments(true);
      const userDocs = await DocumentService.getDocumentsByStatus(currentUser.uid, 'completed');
      setDocuments(userDocs);
    } catch (error) {
      console.error('Error loading documents:', error);
    } finally {
      setLoadingDocuments(false);
    }
  }, [currentUser]);

  useEffect(() => {
    if (currentUser) {
      loadUserDocuments();
    }
  }, [currentUser, loadUserDocuments]);

  // Validate inputs when variables change to provide real-time feedback
  useEffect(() => {
    if (Object.keys(variables).length > 0) {
      const errors: Record<string, string> = {};

      for (const variable of prompt.variables) {
        if (variable.required && !variables[variable.name]) {
          errors[variable.name] = `${variable.name} is required`;
        }
      }

      setValidationErrors(errors);
    }
  }, [variables, prompt.variables]);

  const handleVariableChange = (variableName: string, value: string | number | boolean | string[]) => {
    setVariables(prev => ({
      ...prev,
      [variableName]: value
    }));

    // Clear validation error for this field if it now has a value
    if (validationErrors[variableName] && value) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[variableName];
        return newErrors;
      });
    }
  };

  const validateInputs = () => {
    const errors: Record<string, string> = {};

    for (const variable of prompt.variables) {
      if (variable.required && !variables[variable.name]) {
        errors[variable.name] = `${variable.name} is required`;
      }
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const executePrompt = async () => {
    if (!currentUser || !validateInputs()) return;

    try {
      setExecuting(true);
      setResult(null);
      setMultiModelResult(null);
      setExecutionError(null);
      setValidationErrors({});

      const startTime = Date.now();

      // Prepare the prompt content with variables
      let processedPrompt = prompt.content;
      Object.entries(variables).forEach(([key, value]) => {
        const placeholder = `{{${key}}}`;
        processedPrompt = processedPrompt.replace(new RegExp(placeholder, 'g'), String(value));
      });

      // Prepare RAG context if using RAG
      let ragContext = '';
      if (settings.useRAG && selectedDocuments.length > 0) {
        // This would typically retrieve context from the selected documents
        ragContext = `Using documents: ${selectedDocuments.join(', ')}`;
      }

      if (settings.useMultiModel && settings.models.length > 1) {
        // Multi-model execution
        const executeMultiModelFunction = httpsCallable(functions, 'execute_multi_model_prompt');
        const response = await executeMultiModelFunction({
          prompt: processedPrompt,
          models: settings.models,
          systemPrompt: prompt.systemPrompt,
          context: ragContext
        });

        const executionData = response.data as any;

        if (executionData.success) {
          setMultiModelResult(executionData);
        } else {
          throw new Error(executionData.error || 'Multi-model execution failed');
        }
      } else {
        // Single model execution with bulletproof fallback
        let response;

        try {
          console.log('🚀 Executing prompt with Firebase Functions...');

          // Call the actual Firebase Functions endpoint
          const executePrompt = httpsCallable(functions, 'api');
          response = await executePrompt({
            endpoint: 'execute_prompt',
            promptId: prompt.id,
            inputs: variables,
            useRag: settings.useRAG,
            ragQuery: settings.useRAG ? variables['query'] || processedPrompt : '',
            documentIds: selectedDocuments || [],
            models: settings.models,
            temperature: settings.temperature,
            maxTokens: settings.maxTokens
          });

          console.log('✅ Firebase Functions response received:', response);
        } catch (error) {
          console.error('❌ Firebase Functions call failed:', error);

          // Enhanced error handling with fallback strategies
          let errorMessage = 'Unknown error occurred';
          let fallbackStrategy = 'none';

          if (error.code === 'functions/unauthenticated') {
            errorMessage = 'Authentication required. Please sign in and try again.';
            fallbackStrategy = 'reauthenticate';
          } else if (error.code === 'functions/permission-denied') {
            errorMessage = 'Permission denied. Please check your account permissions.';
            fallbackStrategy = 'contact_support';
          } else if (error.code === 'functions/deadline-exceeded' || error.code === 'functions/timeout') {
            errorMessage = 'Request timed out. The AI service may be experiencing high load.';
            fallbackStrategy = 'retry_later';
          } else if (error.code === 'functions/unavailable') {
            errorMessage = 'AI service temporarily unavailable. Please try again in a few moments.';
            fallbackStrategy = 'retry_with_delay';
          } else if (error.code === 'functions/resource-exhausted') {
            errorMessage = 'Rate limit exceeded. Please wait before making another request.';
            fallbackStrategy = 'rate_limited';
          } else if (error.message?.includes('CORS')) {
            errorMessage = 'Network connectivity issue. Trying alternative endpoint...';
            fallbackStrategy = 'try_http_endpoint';
          } else {
            errorMessage = `AI service error: ${error.message || 'Please try again'}`;
            fallbackStrategy = 'generic_retry';
          }

          // Attempt fallback strategies
          if (fallbackStrategy === 'try_http_endpoint') {
            try {
              console.log('🔄 Attempting HTTP endpoint fallback...');
              const httpResponse = await fetch(`https://australia-southeast1-${process.env.VITE_FIREBASE_PROJECT_ID}.cloudfunctions.net/execute_prompt_http`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  data: {
                    promptId: prompt.id,
                    inputs: variables,
                    useRag: settings.useRAG,
                    ragQuery: settings.useRAG ? variables['query'] || processedPrompt : '',
                    documentIds: selectedDocuments || [],
                    models: settings.models,
                    temperature: settings.temperature,
                    maxTokens: settings.maxTokens
                  }
                })
              });

              if (httpResponse.ok) {
                const httpData = await httpResponse.json();
                response = { data: httpData };
                console.log('✅ HTTP endpoint fallback successful');
              } else {
                throw new Error(`HTTP ${httpResponse.status}: ${httpResponse.statusText}`);
              }
            } catch (httpError) {
              console.error('❌ HTTP endpoint fallback also failed:', httpError);
              response = {
                data: {
                  success: false,
                  error: `Both primary and fallback endpoints failed: ${errorMessage}`,
                  fallbackAttempted: true,
                  metadata: {
                    promptId: prompt.id,
                    userId: currentUser?.uid,
                    inputs: variables,
                    executedAt: new Date().toISOString(),
                    error: errorMessage,
                    fallbackStrategy,
                    originalError: error.message,
                    httpFallbackError: httpError.message
                  }
                }
              };
            }
          } else {
            // Standard error response with fallback guidance
            response = {
              data: {
                success: false,
                error: errorMessage,
                fallbackStrategy,
                metadata: {
                  promptId: prompt.id,
                  userId: currentUser?.uid,
                  inputs: variables,
                  executedAt: new Date().toISOString(),
                  error: errorMessage,
                  fallbackStrategy,
                  originalError: error.message
                }
              }
            };
          }
        }

        const executionData = response.data as any;

        const endTime = Date.now();
        const executionTime = (endTime - startTime) / 1000;

        // Create execution result from Firebase Function response
        const execution: PromptExecution = {
          id: `exec-${Date.now()}`,
          promptId: prompt.id,
          inputs: variables,
          outputs: {
            content: executionData.output || 'No response generated',
            metadata: {
              model: executionData.metadata?.model || (settings.models && settings.models.length > 0 ? settings.models[0] : 'unknown'),
              tokensUsed: executionData.metadata?.tokensUsed || 0,
              executionTime: executionData.metadata?.executionTime || executionTime,
              cost: executionData.metadata?.cost || 0
            }
          },
          timestamp: new Date(),
          status: executionData.metadata?.error ? 'failed' : 'completed',
          error: executionData.metadata?.error
        };

        setResult(execution);

        if (onExecutionComplete) {
          onExecutionComplete(execution);
        }
      }

    } catch (error) {
      console.error('Execution error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to execute prompt. Please try again.';
      setExecutionError(errorMessage);
    } finally {
      setExecuting(false);
    }
  };

  const testOpenRouterConnection = async () => {
    if (!currentUser) return;

    try {
      setTestingConnection(true);

      // Call actual Firebase Functions test endpoint
      console.log('🚀 Testing OpenRouter connection via Firebase Functions...');

      const testConnection = httpsCallable(functions, 'api');
      const response = await testConnection({
        endpoint: 'test_openrouter_connection'
      });

      console.log('✅ OpenRouter test response received:', response);

      const testData = response.data as any;

      if (testData.status === 'success') {
        alert(`✅ OpenRouter Connection Successful!\n\nModel: ${testData.model_info?.model}\nTest Response: ${testData.test_response?.content}\nTokens Used: ${testData.test_response?.tokens_used}\nResponse Time: ${testData.test_response?.response_time?.toFixed(2)}s`);
      } else {
        alert(`❌ OpenRouter Connection Failed!\n\nError: ${testData.message}`);
      }

    } catch (error) {
      console.error('Connection test error:', error);
      alert('❌ Failed to test OpenRouter connection. Please check the console for details.');
    } finally {
      setTestingConnection(false);
    }
  };

  const renderVariableInput = (variable: PromptVariable) => {
    const value = variables[variable.name] || variable.defaultValue || '';
    const inputId = `variable-${variable.name}`;
    const hasError = validationErrors[variable.name];
    const baseClassName = `block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
      hasError ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''
    }`;

    switch (variable.type) {
      case 'boolean':
        return (
          <input
            id={inputId}
            type="checkbox"
            checked={Boolean(value)}
            onChange={(e) => handleVariableChange(variable.name, e.target.checked)}
            className={`rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 ${
              hasError ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''
            }`}
            aria-describedby={hasError ? `${inputId}-error` : undefined}
          />
        );
      case 'number':
        return (
          <input
            id={inputId}
            type="number"
            value={typeof value === 'number' ? value : ''}
            onChange={(e) => handleVariableChange(variable.name, parseFloat(e.target.value) || 0)}
            className={baseClassName}
            placeholder={`Enter ${variable.name}`}
            aria-describedby={hasError ? `${inputId}-error` : undefined}
          />
        );
      case 'array':
        return (
          <textarea
            id={inputId}
            value={Array.isArray(value) ? value.join('\n') : String(value)}
            onChange={(e) => handleVariableChange(variable.name, e.target.value.split('\n').filter(Boolean))}
            rows={3}
            className={baseClassName}
            placeholder="Enter one item per line"
            aria-describedby={hasError ? `${inputId}-error` : undefined}
          />
        );
      default:
        return (
          <input
            id={inputId}
            type="text"
            value={String(value)}
            onChange={(e) => handleVariableChange(variable.name, e.target.value)}
            className={baseClassName}
            placeholder={`Enter ${variable.name}`}
            aria-describedby={hasError ? `${inputId}-error` : undefined}
          />
        );
    }
  };

  return (
    <div className="space-y-6">
      {/* Prompt Info */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
          {prompt.title}
        </h2>
        {prompt.description && (
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {prompt.description}
          </p>
        )}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-md p-4">
          <pre className="whitespace-pre-wrap text-sm text-gray-900 dark:text-white font-mono">
            {prompt.content}
          </pre>
        </div>
      </div>

      {/* Variables */}
      {prompt.variables.length > 0 && (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Variables
          </h3>
          <div className="space-y-4">
            {prompt.variables.map((variable) => {
              const inputId = `variable-${variable.name}`;
              const hasError = validationErrors[variable.name];

              return (
                <div key={variable.name}>
                  <label
                    htmlFor={inputId}
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                  >
                    {variable.name}
                    {variable.required && <span className="text-red-500 ml-1">*</span>}
                  </label>
                  {variable.description && (
                    <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                      {variable.description}
                    </p>
                  )}
                  {renderVariableInput(variable)}
                  {hasError && (
                    <div className="mt-1 flex items-center text-sm text-red-600">
                      <AlertCircle className="w-4 h-4 mr-1" />
                      {hasError}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Settings */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <button
            onClick={() => setShowSettings(!showSettings)}
            className="flex items-center justify-between w-full text-left"
          >
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Execution Settings
            </h3>
            <Settings className="h-5 w-5 text-gray-400" />
          </button>
        </div>
        
        {showSettings && (
          <div className="p-6 space-y-6">
            {/* Model Selection */}
            <div>
              <ModelSelector
                selectedModels={settings.models}
                onModelSelect={(models) => setSettings(prev => ({ ...prev, models }))}
                showComparison={settings.useMultiModel}
                onComparisonToggle={(enabled) => setSettings(prev => ({ ...prev, useMultiModel: enabled }))}
              />
            </div>

            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">

              <div>
                <label
                  htmlFor="temperature-slider"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >
                  Temperature: {settings.temperature}
                </label>
                <input
                  id="temperature-slider"
                  type="range"
                  min="0"
                  max="2"
                  step="0.1"
                  value={settings.temperature}
                  onChange={(e) => setSettings(prev => ({ ...prev, temperature: parseFloat(e.target.value) }))}
                  className="w-full"
                />
              </div>

              <div>
                <label
                  htmlFor="max-tokens-input"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >
                  Max Tokens
                </label>
                <input
                  id="max-tokens-input"
                  type="number"
                  value={settings.maxTokens}
                  onChange={(e) => setSettings(prev => ({ ...prev, maxTokens: parseInt(e.target.value) || 1000 }))}
                  className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>

              <div>
                <label htmlFor="use-rag-checkbox" className="flex items-center">
                  <input
                    id="use-rag-checkbox"
                    type="checkbox"
                    checked={settings.useRAG}
                    onChange={(e) => setSettings(prev => ({ ...prev, useRAG: e.target.checked }))}
                    className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                  />
                  <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                    Use RAG (Retrieval Augmented Generation)
                  </span>
                </label>
              </div>

              {/* Document Selection for RAG */}
              {settings.useRAG && (
                <div className="border-t border-gray-200 dark:border-gray-600 pt-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Select Documents for Context
                  </label>

                  {loadingDocuments ? (
                    <div className="flex items-center justify-center py-4">
                      <LoadingSpinner size="sm" />
                      <span className="ml-2 text-sm text-gray-600">Loading documents...</span>
                    </div>
                  ) : documents.length === 0 ? (
                    <div className="text-sm text-gray-500 py-4 text-center">
                      <FileText className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                      No processed documents available.
                      <br />
                      <a href="/documents" className="text-blue-600 hover:text-blue-700">
                        Upload documents
                      </a> to use RAG.
                    </div>
                  ) : (
                    <div className="space-y-2 max-h-40 overflow-y-auto">
                      <div className="flex items-center mb-2">
                        <button
                          type="button"
                          onClick={() => setSelectedDocuments(
                            selectedDocuments.length === documents.length ? [] : documents.map(d => d.id)
                          )}
                          className="text-sm text-blue-600 hover:text-blue-700"
                        >
                          {selectedDocuments.length === documents.length ? 'Deselect All' : 'Select All'}
                        </button>
                        <span className="ml-2 text-xs text-gray-500">
                          ({selectedDocuments.length} of {documents.length} selected)
                        </span>
                      </div>

                      {documents.map((doc) => {
                        const checkboxId = `document-${doc.id}`;
                        return (
                          <label key={doc.id} htmlFor={checkboxId} className="flex items-center p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded cursor-pointer">
                            <input
                              id={checkboxId}
                              type="checkbox"
                              checked={selectedDocuments.includes(doc.id)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setSelectedDocuments(prev => [...prev, doc.id]);
                                } else {
                                  setSelectedDocuments(prev => prev.filter(id => id !== doc.id));
                                }
                              }}
                              className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                            />
                            <div className="ml-2 flex-1 min-w-0">
                              <div className="text-sm font-medium text-gray-900 dark:text-white truncate">
                                {doc.originalName}
                              </div>
                              <div className="text-xs text-gray-500 dark:text-gray-400">
                                {DocumentService.formatFileSize(doc.size)} •
                                {doc.metadata.chunk_count || 0} chunks
                              </div>
                            </div>
                          </label>
                        );
                      })}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Execute Buttons */}
      <div className="flex justify-center space-x-4">
        <Button
          variant="secondary"
          size="md"
          onClick={testOpenRouterConnection}
          loading={testingConnection}
          disabled={testingConnection || executing}
        >
          {testingConnection ? (
            <>
              <LoadingSpinner size="sm" className="mr-2" />
              Testing...
            </>
          ) : (
            <>
              <Brain className="w-4 h-4 mr-2" />
              Test Connection
            </>
          )}
        </Button>

        <Button
          variant="primary"
          size="lg"
          onClick={executePrompt}
          loading={executing}
          disabled={!currentUser || executing || testingConnection || Object.keys(validationErrors).length > 0}
        >
          {executing ? (
            <>
              <LoadingSpinner size="sm" className="mr-2" />
              Executing...
            </>
          ) : (
            <>
              <Play className="w-5 h-5 mr-2" />
              Execute Prompt
            </>
          )}
        </Button>
      </div>

      {/* Execution Error */}
      {executionError && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400 mr-2" />
            <div>
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                Execution Failed
              </h3>
              <p className="text-sm text-red-700 dark:text-red-300 mt-1">
                {executionError}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Authentication Warning */}
      {!currentUser && (
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-2" />
            <div>
              <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                Authentication Required
              </h3>
              <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                Please sign in to execute prompts.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Results */}
      {result && (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Execution Result
            </h3>
            <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
              <div className="flex items-center">
                <Clock className="w-4 h-4 mr-1" />
                {result.outputs.metadata.executionTime.toFixed(2)}s
              </div>
              <div className="flex items-center">
                <Zap className="w-4 h-4 mr-1" />
                {result.outputs.metadata.tokensUsed} tokens
              </div>
              <div className="flex items-center">
                <DollarSign className="w-4 h-4 mr-1" />
                ${result.outputs.metadata.cost.toFixed(4)}
              </div>
              {result.outputs.metadata.model && (
                <div className="flex items-center">
                  <Brain className="w-4 h-4 mr-1" />
                  {result.outputs.metadata.model?.split('/').pop()?.split(':')[0] || 'Unknown'}
                </div>
              )}
            </div>
          </div>

          {/* RAG Context Information */}
          {result.context && result.ragMetadata && (
            <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
              <div className="flex items-center mb-2">
                <Search className="w-4 h-4 text-blue-600 mr-2" />
                <span className="text-sm font-medium text-blue-900 dark:text-blue-100">
                  RAG Context Used
                </span>
              </div>
              <div className="text-xs text-blue-800 dark:text-blue-200 space-y-1">
                <div>
                  Found {result.ragMetadata.total_chunks_found || 0} relevant chunks,
                  used {result.ragMetadata.chunks_used || 0} for context
                </div>
                {result.ragMetadata.document_sources && result.ragMetadata.document_sources.length > 0 && (
                  <div>
                    Sources: {result.ragMetadata.document_sources.length} document(s)
                  </div>
                )}
                {result.ragMetadata.context_length && (
                  <div>
                    Context length: {result.ragMetadata.context_length} characters
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Error Display */}
          {result.error && (
            <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
              <div className="text-sm font-medium text-red-900 dark:text-red-100 mb-1">
                Execution Error
              </div>
              <div className="text-sm text-red-800 dark:text-red-200">
                {result.error}
              </div>
            </div>
          )}

          <div className="bg-gray-50 dark:bg-gray-700 rounded-md p-4">
            <pre className="whitespace-pre-wrap text-sm text-gray-900 dark:text-white">
              {result.outputs.content}
            </pre>
          </div>

          {/* Show RAG Context Details (Collapsible) */}
          {result.context && (
            <details className="mt-4">
              <summary className="cursor-pointer text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">
                View Retrieved Context ({result.context.length} characters)
              </summary>
              <div className="mt-2 p-3 bg-gray-100 dark:bg-gray-600 rounded text-xs text-gray-700 dark:text-gray-300 max-h-40 overflow-y-auto">
                <pre className="whitespace-pre-wrap">{result.context}</pre>
              </div>
            </details>
          )}
        </div>
      )}

      {/* Multi-Model Results */}
      {multiModelResult && (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <ModelComparison
            results={multiModelResult.results}
            bestModel={multiModelResult.bestModel}
            totalCost={multiModelResult.totalCost}
            executionTime={multiModelResult.executionTime}
            comparisonMetrics={multiModelResult.comparisonMetrics}
          />
        </div>
      )}
    </div>
  );
};
