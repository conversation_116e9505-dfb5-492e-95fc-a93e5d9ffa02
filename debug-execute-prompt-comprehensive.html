<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Execute Prompt Debug Tool</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #fafafa;
        }
        .test-section h3 {
            margin-top: 0;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .button {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: background 0.3s;
        }
        .button:hover { background: #2980b9; }
        .button:disabled { background: #bdc3c7; cursor: not-allowed; }
        .button.danger { background: #e74c3c; }
        .button.danger:hover { background: #c0392b; }
        .button.success { background: #27ae60; }
        .button.success:hover { background: #229954; }
        .log-output {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin: 10px 0;
        }
        .input-group {
            margin: 15px 0;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #2c3e50;
        }
        .input-group input, .input-group textarea, .input-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .input-group textarea {
            height: 100px;
            resize: vertical;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            width: 0%;
            transition: width 0.3s ease;
        }
        .checklist {
            list-style: none;
            padding: 0;
        }
        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .checklist li:before {
            content: "❌ ";
            margin-right: 10px;
        }
        .checklist li.passed:before {
            content: "✅ ";
        }
        .checklist li.warning:before {
            content: "⚠️ ";
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Execute Prompt Debug Tool</h1>
            <p>Comprehensive diagnostic tool for troubleshooting Execute Prompt button issues</p>
        </div>

        <!-- Quick Status Overview -->
        <div class="test-section">
            <h3>📊 Quick Status Overview</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="overallProgress"></div>
            </div>
            <ul class="checklist" id="statusChecklist">
                <li id="auth-status">Authentication Status</li>
                <li id="firebase-status">Firebase Connection</li>
                <li id="functions-status">Cloud Functions</li>
                <li id="openrouter-status">OpenRouter API</li>
                <li id="firestore-status">Firestore Access</li>
                <li id="button-status">Button Functionality</li>
            </ul>
        </div>

        <!-- Test Configuration -->
        <div class="test-section">
            <h3>⚙️ Test Configuration</h3>
            <div class="grid">
                <div>
                    <div class="input-group">
                        <label for="testPromptId">Test Prompt ID:</label>
                        <input type="text" id="testPromptId" placeholder="Enter prompt ID to test">
                    </div>
                    <div class="input-group">
                        <label for="testPromptContent">Test Prompt Content:</label>
                        <textarea id="testPromptContent" placeholder="Enter test prompt content">Hello, this is a test prompt with variable: {{test_var}}</textarea>
                    </div>
                </div>
                <div>
                    <div class="input-group">
                        <label for="testVariable">Test Variable Value:</label>
                        <input type="text" id="testVariable" placeholder="Enter test variable value" value="test value">
                    </div>
                    <div class="input-group">
                        <label for="testModel">AI Model:</label>
                        <select id="testModel">
                            <option value="meta-llama/llama-3.2-11b-vision-instruct:free">Llama 3.2 11B (Free)</option>
                            <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                            <option value="gpt-4">GPT-4</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Individual Tests -->
        <div class="test-section">
            <h3>🧪 Individual Tests</h3>
            <div class="grid">
                <div>
                    <button class="button" onclick="testAuthentication()">1. Test Authentication</button>
                    <button class="button" onclick="testFirebaseConnection()">2. Test Firebase Connection</button>
                    <button class="button" onclick="testCloudFunctions()">3. Test Cloud Functions</button>
                </div>
                <div>
                    <button class="button" onclick="testOpenRouterAPI()">4. Test OpenRouter API</button>
                    <button class="button" onclick="testFirestoreAccess()">5. Test Firestore Access</button>
                    <button class="button" onclick="testButtonFunctionality()">6. Test Button Functionality</button>
                </div>
            </div>
        </div>

        <!-- Comprehensive Test -->
        <div class="test-section">
            <h3>🚀 Comprehensive Test</h3>
            <button class="button success" onclick="runComprehensiveTest()">Run All Tests</button>
            <button class="button" onclick="testExecutePromptFlow()">Test Execute Prompt Flow</button>
            <button class="button danger" onclick="clearLogs()">Clear Logs</button>
        </div>

        <!-- Console Output -->
        <div class="test-section">
            <h3>📝 Debug Console</h3>
            <div class="log-output" id="debugConsole">
Ready to start debugging...
Click "Run All Tests" to begin comprehensive testing.
            </div>
        </div>

        <!-- Troubleshooting Guide -->
        <div class="test-section">
            <h3>🔍 Common Issues & Solutions</h3>
            <div id="troubleshootingGuide">
                <div class="status info">
                    <strong>Loading troubleshooting guide...</strong><br>
                    Run tests first to get specific recommendations.
                </div>
            </div>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getAuth, onAuthStateChanged, signInAnonymously } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
        import { getFirestore, doc, getDoc, collection, addDoc } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';
        import { getFunctions, httpsCallable } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-functions.js';

        // Firebase configuration - you'll need to update this with your config
        const firebaseConfig = {
            // Add your Firebase config here
            apiKey: "your-api-key",
            authDomain: "your-project.firebaseapp.com",
            projectId: "your-project-id",
            storageBucket: "your-project.appspot.com",
            messagingSenderId: "*********",
            appId: "your-app-id"
        };

        // Initialize Firebase
        let app, auth, db, functions;
        let currentUser = null;
        let testResults = {};

        try {
            app = initializeApp(firebaseConfig);
            auth = getAuth(app);
            db = getFirestore(app);
            functions = getFunctions(app, 'australia-southeast1');
            
            // Make Firebase objects available globally
            window.firebaseApp = app;
            window.firebaseAuth = auth;
            window.firebaseDb = db;
            window.firebaseFunctions = functions;
            
            log('✅ Firebase SDK initialized successfully');
        } catch (error) {
            log(`❌ Firebase initialization failed: ${error.message}`, 'error');
        }

        // Auth state listener
        onAuthStateChanged(auth, (user) => {
            currentUser = user;
            updateAuthStatus(user);
        });

        // Global functions
        window.testAuthentication = testAuthentication;
        window.testFirebaseConnection = testFirebaseConnection;
        window.testCloudFunctions = testCloudFunctions;
        window.testOpenRouterAPI = testOpenRouterAPI;
        window.testFirestoreAccess = testFirestoreAccess;
        window.testButtonFunctionality = testButtonFunctionality;
        window.runComprehensiveTest = runComprehensiveTest;
        window.testExecutePromptFlow = testExecutePromptFlow;
        window.clearLogs = clearLogs;

        function log(message, type = 'info') {
            const console = document.getElementById('debugConsole');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            console.textContent += `\n[${timestamp}] ${prefix} ${message}`;
            console.scrollTop = console.scrollHeight;
        }

        function updateStatus(elementId, passed, message = '') {
            const element = document.getElementById(elementId);
            if (element) {
                element.className = passed ? 'passed' : (message.includes('warning') ? 'warning' : '');
                if (message) {
                    element.textContent = element.textContent.split(':')[0] + ': ' + message;
                }
            }
        }

        function updateAuthStatus(user) {
            if (user) {
                updateStatus('auth-status', true, 'Authenticated');
                log(`✅ User authenticated: ${user.uid}`);
            } else {
                updateStatus('auth-status', false, 'Not authenticated');
                log('❌ User not authenticated');
            }
        }

        async function testAuthentication() {
            log('🔍 Testing authentication...');
            try {
                if (!currentUser) {
                    log('🔄 Attempting anonymous sign-in...');
                    await signInAnonymously(auth);
                }
                testResults.auth = true;
                updateStatus('auth-status', true, 'Authenticated');
                log('✅ Authentication test passed');
                return true;
            } catch (error) {
                testResults.auth = false;
                updateStatus('auth-status', false, 'Failed');
                log(`❌ Authentication test failed: ${error.message}`, 'error');
                return false;
            }
        }

        async function testFirebaseConnection() {
            log('🔍 Testing Firebase connection...');
            try {
                // Test basic Firebase connectivity
                const testDoc = doc(db, 'test', 'connection');
                await getDoc(testDoc);
                
                testResults.firebase = true;
                updateStatus('firebase-status', true, 'Connected');
                log('✅ Firebase connection test passed');
                return true;
            } catch (error) {
                testResults.firebase = false;
                updateStatus('firebase-status', false, 'Failed');
                log(`❌ Firebase connection test failed: ${error.message}`, 'error');
                return false;
            }
        }

        async function testCloudFunctions() {
            log('🔍 Testing Cloud Functions...');
            try {
                const healthCheck = httpsCallable(functions, 'api');
                const response = await healthCheck({ endpoint: 'health' });
                
                if (response.data && response.data.status === 'success') {
                    testResults.functions = true;
                    updateStatus('functions-status', true, 'Working');
                    log('✅ Cloud Functions test passed');
                    log(`📊 Function response: ${JSON.stringify(response.data)}`);
                    return true;
                } else {
                    throw new Error('Invalid response from health check');
                }
            } catch (error) {
                testResults.functions = false;
                updateStatus('functions-status', false, 'Failed');
                log(`❌ Cloud Functions test failed: ${error.message}`, 'error');
                return false;
            }
        }

        async function testOpenRouterAPI() {
            log('🔍 Testing OpenRouter API...');
            try {
                const testConnection = httpsCallable(functions, 'api');
                const response = await testConnection({ endpoint: 'test_openrouter_connection' });
                
                if (response.data && response.data.status === 'success') {
                    testResults.openrouter = true;
                    updateStatus('openrouter-status', true, 'Connected');
                    log('✅ OpenRouter API test passed');
                    log(`📊 API response: ${JSON.stringify(response.data)}`);
                    return true;
                } else {
                    throw new Error(response.data?.message || 'OpenRouter test failed');
                }
            } catch (error) {
                testResults.openrouter = false;
                updateStatus('openrouter-status', false, 'Failed');
                log(`❌ OpenRouter API test failed: ${error.message}`, 'error');
                return false;
            }
        }

        async function testFirestoreAccess() {
            log('🔍 Testing Firestore access...');
            try {
                if (!currentUser) {
                    throw new Error('User not authenticated');
                }

                // Test reading from user's prompts collection
                const promptsRef = collection(db, 'users', currentUser.uid, 'prompts');
                // Just test collection access, don't need to read actual documents
                
                testResults.firestore = true;
                updateStatus('firestore-status', true, 'Accessible');
                log('✅ Firestore access test passed');
                return true;
            } catch (error) {
                testResults.firestore = false;
                updateStatus('firestore-status', false, 'Failed');
                log(`❌ Firestore access test failed: ${error.message}`, 'error');
                return false;
            }
        }

        async function testButtonFunctionality() {
            log('🔍 Testing button functionality...');
            try {
                // Check if we're on the right page or can simulate button behavior
                const executeButtons = document.querySelectorAll('button');
                const hasExecuteButton = Array.from(executeButtons).some(btn => 
                    btn.textContent.toLowerCase().includes('execute')
                );

                if (hasExecuteButton) {
                    testResults.button = true;
                    updateStatus('button-status', true, 'Found');
                    log('✅ Execute button found on page');
                } else {
                    testResults.button = false;
                    updateStatus('button-status', false, 'Not found');
                    log('⚠️ Execute button not found - may need to navigate to prompt execution page', 'warning');
                }
                return testResults.button;
            } catch (error) {
                testResults.button = false;
                updateStatus('button-status', false, 'Error');
                log(`❌ Button functionality test failed: ${error.message}`, 'error');
                return false;
            }
        }

        async function runComprehensiveTest() {
            log('🚀 Starting comprehensive test suite...');
            const tests = [
                testAuthentication,
                testFirebaseConnection,
                testCloudFunctions,
                testOpenRouterAPI,
                testFirestoreAccess,
                testButtonFunctionality
            ];

            let passed = 0;
            for (let i = 0; i < tests.length; i++) {
                const result = await tests[i]();
                if (result) passed++;
                
                // Update progress bar
                const progress = ((i + 1) / tests.length) * 100;
                document.getElementById('overallProgress').style.width = `${progress}%`;
                
                // Small delay between tests
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            log(`📊 Test suite completed: ${passed}/${tests.length} tests passed`);
            generateTroubleshootingGuide();
        }

        async function testExecutePromptFlow() {
            log('🔍 Testing complete execute prompt flow...');
            
            if (!currentUser) {
                log('❌ Cannot test execute prompt flow: User not authenticated', 'error');
                return;
            }

            try {
                const promptContent = document.getElementById('testPromptContent').value;
                const testVar = document.getElementById('testVariable').value;
                const model = document.getElementById('testModel').value;

                log('📝 Test parameters:');
                log(`   Prompt: ${promptContent}`);
                log(`   Variable: ${testVar}`);
                log(`   Model: ${model}`);

                // Simulate the execute prompt call
                const executePrompt = httpsCallable(functions, 'api');
                const response = await executePrompt({
                    endpoint: 'execute_prompt',
                    promptId: 'test-prompt-id',
                    inputs: { test_var: testVar },
                    useRag: false,
                    ragQuery: '',
                    documentIds: [],
                    models: [model],
                    temperature: 0.7,
                    maxTokens: 1000
                });

                log('✅ Execute prompt flow test completed');
                log(`📊 Response: ${JSON.stringify(response.data, null, 2)}`);

            } catch (error) {
                log(`❌ Execute prompt flow test failed: ${error.message}`, 'error');
                log(`🔍 Error details: ${JSON.stringify(error, null, 2)}`);
            }
        }

        function generateTroubleshootingGuide() {
            const guide = document.getElementById('troubleshootingGuide');
            let recommendations = [];

            if (!testResults.auth) {
                recommendations.push({
                    type: 'error',
                    title: 'Authentication Issue',
                    solution: 'Check Firebase Auth configuration and ensure user is signed in before executing prompts.'
                });
            }

            if (!testResults.firebase) {
                recommendations.push({
                    type: 'error',
                    title: 'Firebase Connection Issue',
                    solution: 'Verify Firebase configuration in your app. Check network connectivity and Firebase project settings.'
                });
            }

            if (!testResults.functions) {
                recommendations.push({
                    type: 'error',
                    title: 'Cloud Functions Issue',
                    solution: 'Deploy Cloud Functions using "firebase deploy --only functions". Check function logs for errors.'
                });
            }

            if (!testResults.openrouter) {
                recommendations.push({
                    type: 'error',
                    title: 'OpenRouter API Issue',
                    solution: 'Verify OpenRouter API key is set correctly in Firebase Functions environment variables.'
                });
            }

            if (!testResults.firestore) {
                recommendations.push({
                    type: 'error',
                    title: 'Firestore Access Issue',
                    solution: 'Check Firestore security rules and ensure user has permission to read/write prompts.'
                });
            }

            if (!testResults.button) {
                recommendations.push({
                    type: 'warning',
                    title: 'Button Not Found',
                    solution: 'Navigate to a prompt execution page to test the Execute Prompt button functionality.'
                });
            }

            if (recommendations.length === 0) {
                guide.innerHTML = '<div class="status success"><strong>All tests passed!</strong><br>Your Execute Prompt functionality should be working correctly.</div>';
            } else {
                guide.innerHTML = recommendations.map(rec => 
                    `<div class="status ${rec.type}">
                        <strong>${rec.title}</strong><br>
                        ${rec.solution}
                    </div>`
                ).join('');
            }
        }

        function clearLogs() {
            document.getElementById('debugConsole').textContent = 'Debug console cleared.\nReady for new tests...';
        }

        // Initialize on page load
        window.addEventListener('load', () => {
            log('🔧 Execute Prompt Debug Tool loaded');
            log('📋 Click "Run All Tests" to start comprehensive diagnostics');
        });
    </script>
</body>
</html>
