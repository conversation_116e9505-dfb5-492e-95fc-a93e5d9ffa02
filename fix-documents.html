<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Document Statuses</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .button {
            background-color: #4CAF50;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 0;
        }
        .button:hover {
            background-color: #45a049;
        }
        .button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Fix Document Statuses</h1>
        <p>This tool will update all uploaded documents to "completed" status so they appear in your RAG selection dropdown.</p>
        
        <button id="fixButton" class="button" onclick="fixDocuments()">
            Fix Document Statuses
        </button>
        
        <div id="result"></div>
    </div>

    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getAuth, signInAnonymously } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
        import { getFunctions, httpsCallable } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-functions.js';

        // Firebase config
        const firebaseConfig = {
            apiKey: "AIzaSyDJWjw2e8FayU3CvIWyGXXFAqDCTFN5CJs",
            authDomain: "rag-prompt-library.firebaseapp.com",
            projectId: "rag-prompt-library",
            storageBucket: "rag-prompt-library.firebasestorage.app",
            messagingSenderId: "743998930129",
            appId: "1:743998930129:web:69dd61394ed81598cd99f0"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const functions = getFunctions(app, 'australia-southeast1');

        window.fixDocuments = async function() {
            const button = document.getElementById('fixButton');
            const result = document.getElementById('result');
            
            try {
                button.disabled = true;
                button.textContent = 'Fixing...';
                result.className = 'result loading';
                result.textContent = '🔐 Authenticating and fixing documents...';

                // Sign in anonymously
                await signInAnonymously(auth);
                
                result.textContent = '🔧 Calling fix function...';

                // Call the fix function
                const fixDocuments = httpsCallable(functions, 'fix_document_statuses');
                const response = await fixDocuments();

                if (response.data.success) {
                    result.className = 'result success';
                    result.textContent = `🎉 SUCCESS!\n\n` +
                        `📄 Fixed ${response.data.documentsFixed} documents\n\n` +
                        `Documents fixed:\n${response.data.documentNames ? response.data.documentNames.map(name => `  - ${name}`).join('\n') : 'None'}\n\n` +
                        `✅ Your documents should now appear in the RAG selection dropdown!\n` +
                        `🔄 Refresh your prompt execution page to see the documents.`;
                } else {
                    result.className = 'result error';
                    result.textContent = `❌ Fix failed: ${response.data.error}`;
                }

            } catch (error) {
                result.className = 'result error';
                result.textContent = `❌ Error: ${error.message}`;
            } finally {
                button.disabled = false;
                button.textContent = 'Fix Document Statuses';
            }
        };
    </script>
</body>
</html>
