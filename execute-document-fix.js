const { initializeApp } = require('firebase/app');
const { getAuth, signInAnonymously } = require('firebase/auth');
const { getFunctions, httpsCallable } = require('firebase/functions');

// Firebase config from your project
const firebaseConfig = {
  apiKey: "AIzaSyDJWjw2e8FayU3CvIWyGXXFAqDCTFN5CJs",
  authDomain: "rag-prompt-library.firebaseapp.com",
  projectId: "rag-prompt-library",
  storageBucket: "rag-prompt-library.firebasestorage.app",
  messagingSenderId: "743998930129",
  appId: "1:743998930129:web:69dd61394ed81598cd99f0"
};

async function executeDocumentFix() {
  console.log('🔧 Starting document status fix...');
  
  try {
    // Initialize Firebase
    const app = initializeApp(firebaseConfig);
    const auth = getAuth(app);
    const functions = getFunctions(app, 'australia-southeast1');

    console.log('🔐 Authenticating...');
    await signInAnonymously(auth);
    console.log('✅ Authentication successful');

    console.log('📞 Calling fix_document_statuses function...');
    const fixDocuments = httpsCallable(functions, 'fix_document_statuses');
    const result = await fixDocuments();

    console.log('📊 Function Response:', JSON.stringify(result.data, null, 2));

    if (result.data.success) {
      console.log('🎉 SUCCESS!');
      console.log(`📄 Fixed ${result.data.documentsFixed} documents`);
      
      if (result.data.documentNames && result.data.documentNames.length > 0) {
        console.log('📋 Documents updated:');
        result.data.documentNames.forEach((name, index) => {
          console.log(`  ${index + 1}. ${name}`);
        });
      }
      
      console.log('');
      console.log('✅ Your documents should now appear in the RAG selection dropdown!');
      console.log('🔄 Go to https://rag-prompt-library.web.app and check the prompt execution page.');
      
      return {
        success: true,
        documentsFixed: result.data.documentsFixed,
        documentNames: result.data.documentNames
      };
    } else {
      console.log('❌ Fix failed:', result.data.error);
      return {
        success: false,
        error: result.data.error
      };
    }

  } catch (error) {
    console.error('❌ Error executing fix:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

// Execute the fix
executeDocumentFix()
  .then(result => {
    if (result.success) {
      console.log(`\n🎯 TASK COMPLETED: Updated ${result.documentsFixed} documents to 'completed' status`);
    } else {
      console.log(`\n❌ TASK FAILED: ${result.error}`);
    }
    process.exit(result.success ? 0 : 1);
  })
  .catch(error => {
    console.error('❌ Unexpected error:', error);
    process.exit(1);
  });
