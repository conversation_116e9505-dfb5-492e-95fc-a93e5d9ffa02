/**
 * Execute Prompt Diagnostics Script
 * Run this in your browser console while on the React app to diagnose issues
 */

class ExecutePromptDiagnostics {
    constructor() {
        this.results = {};
        this.log('🔧 Execute Prompt Diagnostics Tool Initialized');
        this.log('📋 Run diagnostics.runAll() to start comprehensive testing');
    }

    log(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
        console.log(`[${timestamp}] ${prefix} ${message}`);
    }

    // Test 1: Check if user is authenticated
    async testAuthentication() {
        this.log('🔍 Testing authentication...');
        try {
            // Check if Firebase auth is available
            if (typeof window.firebase === 'undefined' && typeof window.firebaseAuth === 'undefined') {
                throw new Error('Firebase not found in global scope');
            }

            // Try to get current user
            const auth = window.firebaseAuth || window.firebase?.auth?.();
            const currentUser = auth?.currentUser;

            if (currentUser) {
                this.results.auth = true;
                this.log(`✅ User authenticated: ${currentUser.uid}`);
                return true;
            } else {
                this.results.auth = false;
                this.log('❌ User not authenticated', 'error');
                return false;
            }
        } catch (error) {
            this.results.auth = false;
            this.log(`❌ Authentication test failed: ${error.message}`, 'error');
            return false;
        }
    }

    // Test 2: Check Firebase Functions connectivity
    async testFirebaseFunctions() {
        this.log('🔍 Testing Firebase Functions...');
        try {
            // Check if functions are available
            const functions = window.firebaseFunctions || window.firebase?.functions?.();
            if (!functions) {
                throw new Error('Firebase Functions not available');
            }

            // Test health endpoint
            const healthCheck = functions.httpsCallable('api');
            const response = await healthCheck({ endpoint: 'health' });

            if (response.data && response.data.status === 'success') {
                this.results.functions = true;
                this.log('✅ Firebase Functions working');
                this.log(`📊 Response: ${JSON.stringify(response.data)}`);
                return true;
            } else {
                throw new Error('Invalid health check response');
            }
        } catch (error) {
            this.results.functions = false;
            this.log(`❌ Firebase Functions test failed: ${error.message}`, 'error');
            return false;
        }
    }

    // Test 3: Check OpenRouter API connectivity
    async testOpenRouterAPI() {
        this.log('🔍 Testing OpenRouter API...');
        try {
            const functions = window.firebaseFunctions || window.firebase?.functions?.();
            if (!functions) {
                throw new Error('Firebase Functions not available');
            }

            const testConnection = functions.httpsCallable('api');
            const response = await testConnection({ endpoint: 'test_openrouter_connection' });

            if (response.data && response.data.status === 'success') {
                this.results.openrouter = true;
                this.log('✅ OpenRouter API working');
                this.log(`📊 Model: ${response.data.model_info?.model}`);
                return true;
            } else {
                throw new Error(response.data?.message || 'OpenRouter test failed');
            }
        } catch (error) {
            this.results.openrouter = false;
            this.log(`❌ OpenRouter API test failed: ${error.message}`, 'error');
            return false;
        }
    }

    // Test 4: Check Execute Prompt button presence and state
    testExecuteButton() {
        this.log('🔍 Testing Execute Prompt button...');
        try {
            // Look for Execute Prompt button
            const executeButtons = Array.from(document.querySelectorAll('button')).filter(btn => 
                btn.textContent.toLowerCase().includes('execute prompt') ||
                btn.textContent.toLowerCase().includes('execute') && btn.textContent.toLowerCase().includes('prompt')
            );

            if (executeButtons.length === 0) {
                this.results.button = false;
                this.log('❌ Execute Prompt button not found', 'error');
                this.log('💡 Navigate to /prompts/{id}/execute to test button', 'warning');
                return false;
            }

            const button = executeButtons[0];
            const isDisabled = button.disabled;
            const buttonText = button.textContent.trim();

            this.results.button = true;
            this.log(`✅ Execute Prompt button found: "${buttonText}"`);
            this.log(`📊 Button disabled: ${isDisabled}`);

            if (isDisabled) {
                this.log('⚠️ Button is disabled - check validation errors', 'warning');
                this.checkValidationErrors();
            }

            return true;
        } catch (error) {
            this.results.button = false;
            this.log(`❌ Button test failed: ${error.message}`, 'error');
            return false;
        }
    }

    // Test 5: Check for validation errors
    checkValidationErrors() {
        this.log('🔍 Checking for validation errors...');
        try {
            // Look for error messages
            const errorElements = document.querySelectorAll('[class*="error"], [class*="invalid"], .text-red-500, .text-red-600');
            const errors = Array.from(errorElements)
                .map(el => el.textContent.trim())
                .filter(text => text.length > 0);

            if (errors.length > 0) {
                this.log('⚠️ Validation errors found:', 'warning');
                errors.forEach(error => this.log(`   - ${error}`, 'warning'));
            } else {
                this.log('✅ No validation errors found');
            }

            // Check for required fields
            const requiredInputs = document.querySelectorAll('input[required], textarea[required]');
            const emptyRequired = Array.from(requiredInputs).filter(input => !input.value.trim());

            if (emptyRequired.length > 0) {
                this.log('⚠️ Empty required fields found:', 'warning');
                emptyRequired.forEach(input => {
                    const label = input.previousElementSibling?.textContent || input.placeholder || 'Unknown field';
                    this.log(`   - ${label}`, 'warning');
                });
            }

        } catch (error) {
            this.log(`❌ Validation check failed: ${error.message}`, 'error');
        }
    }

    // Test 6: Check console for errors
    checkConsoleErrors() {
        this.log('🔍 Checking browser console for errors...');
        
        // Override console.error temporarily to catch new errors
        const originalError = console.error;
        const errors = [];
        
        console.error = function(...args) {
            errors.push(args.join(' '));
            originalError.apply(console, args);
        };

        // Restore after a short delay
        setTimeout(() => {
            console.error = originalError;
            if (errors.length > 0) {
                this.log('⚠️ Console errors detected:', 'warning');
                errors.forEach(error => this.log(`   - ${error}`, 'warning'));
            } else {
                this.log('✅ No new console errors detected');
            }
        }, 1000);
    }

    // Test 7: Simulate execute prompt call
    async testExecutePromptCall() {
        this.log('🔍 Testing execute prompt API call...');
        try {
            const functions = window.firebaseFunctions || window.firebase?.functions?.();
            if (!functions) {
                throw new Error('Firebase Functions not available');
            }

            const executePrompt = functions.httpsCallable('api');
            const testData = {
                endpoint: 'execute_prompt',
                promptId: 'test-prompt-id',
                inputs: { test_var: 'test value' },
                useRag: false,
                ragQuery: '',
                documentIds: [],
                models: ['meta-llama/llama-3.2-11b-vision-instruct:free'],
                temperature: 0.7,
                maxTokens: 100
            };

            this.log('📤 Sending test execute prompt request...');
            const response = await executePrompt(testData);

            if (response.data) {
                this.results.executeCall = true;
                this.log('✅ Execute prompt API call successful');
                this.log(`📊 Response: ${JSON.stringify(response.data, null, 2)}`);
                return true;
            } else {
                throw new Error('No response data received');
            }
        } catch (error) {
            this.results.executeCall = false;
            this.log(`❌ Execute prompt API call failed: ${error.message}`, 'error');
            this.log(`🔍 Error details: ${JSON.stringify(error, null, 2)}`);
            return false;
        }
    }

    // Test 8: Check network connectivity
    async testNetworkConnectivity() {
        this.log('🔍 Testing network connectivity...');
        try {
            // Test basic internet connectivity
            const response = await fetch('https://www.google.com/favicon.ico', { 
                method: 'HEAD',
                mode: 'no-cors'
            });
            
            this.results.network = true;
            this.log('✅ Network connectivity working');
            return true;
        } catch (error) {
            this.results.network = false;
            this.log(`❌ Network connectivity test failed: ${error.message}`, 'error');
            return false;
        }
    }

    // Run all tests
    async runAll() {
        this.log('🚀 Starting comprehensive diagnostics...');
        
        const tests = [
            { name: 'Authentication', fn: () => this.testAuthentication() },
            { name: 'Network Connectivity', fn: () => this.testNetworkConnectivity() },
            { name: 'Firebase Functions', fn: () => this.testFirebaseFunctions() },
            { name: 'OpenRouter API', fn: () => this.testOpenRouterAPI() },
            { name: 'Execute Button', fn: () => this.testExecuteButton() },
            { name: 'Console Errors', fn: () => this.checkConsoleErrors() },
            { name: 'Execute Prompt Call', fn: () => this.testExecutePromptCall() }
        ];

        let passed = 0;
        for (const test of tests) {
            try {
                const result = await test.fn();
                if (result) passed++;
            } catch (error) {
                this.log(`❌ Test "${test.name}" threw error: ${error.message}`, 'error');
            }
            
            // Small delay between tests
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        this.log(`📊 Diagnostics completed: ${passed}/${tests.length} tests passed`);
        this.generateReport();
    }

    // Generate diagnostic report
    generateReport() {
        this.log('📋 DIAGNOSTIC REPORT', 'info');
        this.log('==================', 'info');

        const issues = [];
        const recommendations = [];

        if (!this.results.auth) {
            issues.push('Authentication failed');
            recommendations.push('1. Check if user is signed in');
            recommendations.push('2. Verify Firebase Auth configuration');
        }

        if (!this.results.network) {
            issues.push('Network connectivity issues');
            recommendations.push('3. Check internet connection');
            recommendations.push('4. Verify firewall/proxy settings');
        }

        if (!this.results.functions) {
            issues.push('Firebase Functions not responding');
            recommendations.push('5. Deploy functions: firebase deploy --only functions');
            recommendations.push('6. Check Firebase Functions logs');
        }

        if (!this.results.openrouter) {
            issues.push('OpenRouter API not working');
            recommendations.push('7. Verify OpenRouter API key in Firebase Functions');
            recommendations.push('8. Check OpenRouter service status');
        }

        if (!this.results.button) {
            issues.push('Execute Prompt button not found');
            recommendations.push('9. Navigate to a prompt execution page');
            recommendations.push('10. Check React component rendering');
        }

        if (!this.results.executeCall) {
            issues.push('Execute prompt API call failed');
            recommendations.push('11. Check function parameters and payload');
            recommendations.push('12. Verify Firestore permissions');
        }

        if (issues.length === 0) {
            this.log('✅ All systems operational!', 'success');
        } else {
            this.log(`❌ Issues found: ${issues.join(', ')}`, 'error');
            this.log('🔧 RECOMMENDATIONS:', 'warning');
            recommendations.forEach(rec => this.log(rec, 'warning'));
        }

        this.log('==================', 'info');
        this.log('💡 For more help, check the browser Network tab and Firebase Functions logs', 'info');
    }

    // Quick fix suggestions
    quickFixes() {
        this.log('🔧 QUICK FIXES TO TRY:', 'info');
        this.log('1. Refresh the page and try again');
        this.log('2. Clear browser cache and cookies');
        this.log('3. Check browser console for errors (F12)');
        this.log('4. Verify you\'re on the correct prompt execution page');
        this.log('5. Try signing out and signing back in');
        this.log('6. Check if all required fields are filled');
        this.log('7. Test with a different browser');
    }

    // Helper to click execute button if found
    clickExecuteButton() {
        const executeButtons = Array.from(document.querySelectorAll('button')).filter(btn => 
            btn.textContent.toLowerCase().includes('execute prompt')
        );

        if (executeButtons.length > 0) {
            const button = executeButtons[0];
            if (!button.disabled) {
                this.log('🖱️ Clicking Execute Prompt button...');
                button.click();
                return true;
            } else {
                this.log('❌ Execute Prompt button is disabled', 'error');
                this.checkValidationErrors();
                return false;
            }
        } else {
            this.log('❌ Execute Prompt button not found', 'error');
            return false;
        }
    }
}

// Initialize diagnostics tool
const diagnostics = new ExecutePromptDiagnostics();

// Make it available globally
window.diagnostics = diagnostics;

// Auto-run basic checks
diagnostics.testExecuteButton();
diagnostics.checkValidationErrors();

console.log('🔧 Execute Prompt Diagnostics Tool Ready!');
console.log('📋 Available commands:');
console.log('   diagnostics.runAll() - Run comprehensive tests');
console.log('   diagnostics.testAuthentication() - Test auth only');
console.log('   diagnostics.testFirebaseFunctions() - Test functions only');
console.log('   diagnostics.testOpenRouterAPI() - Test OpenRouter only');
console.log('   diagnostics.clickExecuteButton() - Try clicking the button');
console.log('   diagnostics.quickFixes() - Show quick fix suggestions');
