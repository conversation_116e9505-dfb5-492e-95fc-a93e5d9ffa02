import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { LoadingSpinner } from '../components/common/LoadingSpinner';
import { analyticsService, AnalyticsData, BasicMetrics, RecentActivity, TopPrompt } from '../services/analyticsService';
import {
  ChartBarIcon,
  ClockIcon,
  DocumentTextIcon,
  PlayIcon,
  CurrencyDollarIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

interface MetricCardProps {
  title: string;
  value: string | number;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  subtitle?: string;
}

const MetricCard: React.FC<MetricCardProps> = ({ title, value, icon: Icon, color, subtitle }) => {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center">
        <div className={`flex-shrink-0 p-3 rounded-md ${color}`}>
          <Icon className="h-6 w-6 text-white" />
        </div>
        <div className="ml-4">
          <h3 className="text-sm font-medium text-gray-500">{title}</h3>
          <p className="text-2xl font-semibold text-gray-900">{value}</p>
          {subtitle && <p className="text-sm text-gray-600">{subtitle}</p>}
        </div>
      </div>
    </div>
  );
};

interface ActivityItemProps {
  activity: RecentActivity;
}

const ActivityItem: React.FC<ActivityItemProps> = ({ activity }) => {
  const getActivityIcon = () => {
    switch (activity.type) {
      case 'execution_run':
        return <PlayIcon className="h-5 w-5 text-blue-500" />;
      case 'document_uploaded':
        return <DocumentTextIcon className="h-5 w-5 text-green-500" />;
      default:
        return <ChartBarIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  return (
    <div className="flex items-center space-x-3 py-3">
      <div className="flex-shrink-0">
        {getActivityIcon()}
      </div>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-gray-900 truncate">
          {activity.title}
        </p>
        <div className="flex items-center space-x-2 text-xs text-gray-500">
          <span>{formatTimestamp(activity.timestamp)}</span>
          {activity.cost && <span>• ${activity.cost.toFixed(3)}</span>}
          {activity.duration && <span>• {activity.duration.toFixed(2)}s</span>}
        </div>
      </div>
    </div>
  );
};

interface TopPromptItemProps {
  prompt: TopPrompt;
}

const TopPromptItem: React.FC<TopPromptItemProps> = ({ prompt }) => {
  return (
    <div className="flex items-center justify-between py-3">
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-gray-900 truncate">
          {prompt.title}
        </p>
        <p className="text-xs text-gray-500">
          {prompt.executionCount} executions • Avg: ${prompt.avgCost.toFixed(3)}
        </p>
      </div>
      <div className="text-right">
        <p className="text-sm font-semibold text-gray-900">
          {prompt.executionCount}
        </p>
        <p className="text-xs text-gray-500">
          {prompt.lastUsed.toLocaleDateString()}
        </p>
      </div>
    </div>
  );
};

export const Analytics: React.FC = () => {
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);

  useEffect(() => {
    const fetchAnalytics = async () => {
      if (!currentUser) {
        setError('User not authenticated');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        const data = await analyticsService.getUserAnalytics(currentUser.uid);
        setAnalyticsData(data);
      } catch (err) {
        console.error('Error fetching analytics:', err);
        setError('Failed to load analytics data. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchAnalytics();
  }, [currentUser]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-red-500" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Error Loading Analytics</h3>
          <p className="mt-1 text-sm text-gray-500">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No Analytics Data</h3>
          <p className="mt-1 text-sm text-gray-500">Start using the platform to see your analytics.</p>
        </div>
      </div>
    );
  }

  const { metrics, recentActivity, topPrompts } = analyticsData;

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Analytics</h1>
          <p className="mt-2 text-sm text-gray-600">
            Overview of your prompt library usage and performance
          </p>
        </div>

        {/* Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <MetricCard
            title="Total Prompts"
            value={metrics.totalPrompts}
            icon={DocumentTextIcon}
            color="bg-blue-500"
          />
          <MetricCard
            title="Total Executions"
            value={metrics.totalExecutions}
            icon={PlayIcon}
            color="bg-green-500"
          />
          <MetricCard
            title="Total Documents"
            value={metrics.totalDocuments}
            icon={DocumentTextIcon}
            color="bg-purple-500"
          />
          <MetricCard
            title="Total Cost"
            value={`$${metrics.totalCost.toFixed(3)}`}
            icon={CurrencyDollarIcon}
            color="bg-yellow-500"
          />
          <MetricCard
            title="Avg Execution Time"
            value={`${metrics.avgExecutionTime.toFixed(2)}s`}
            icon={ClockIcon}
            color="bg-indigo-500"
          />
          <MetricCard
            title="Success Rate"
            value={`${metrics.successRate.toFixed(1)}%`}
            icon={CheckCircleIcon}
            color="bg-green-600"
          />
        </div>

        {/* Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Activity */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Recent Activity</h2>
            </div>
            <div className="px-6 py-4">
              {recentActivity.length > 0 ? (
                <div className="space-y-1">
                  {recentActivity.map((activity) => (
                    <ActivityItem key={activity.id} activity={activity} />
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500 text-center py-8">
                  No recent activity found
                </p>
              )}
            </div>
          </div>

          {/* Top Prompts */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Top Prompts</h2>
            </div>
            <div className="px-6 py-4">
              {topPrompts.length > 0 ? (
                <div className="space-y-1">
                  {topPrompts.map((prompt) => (
                    <TopPromptItem key={prompt.id} prompt={prompt} />
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500 text-center py-8">
                  No prompt usage data found
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
