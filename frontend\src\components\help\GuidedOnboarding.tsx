/**
 * Guided Onboarding Component
 * Provides step-by-step onboarding for new users after authentication
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { CheckCircleIcon, PlayCircleIcon, DocumentTextIcon, CogIcon } from '@heroicons/react/24/outline';
import { useHelp } from './HelpSystem';
import { useAuth } from '../../contexts/AuthContext';

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  action: string;
  completed: boolean;
  optional?: boolean;
}

interface OnboardingData {
  currentStep: number;
  completedSteps: string[];
  skippedOnboarding: boolean;
  userId?: string; // Track which user this onboarding data belongs to
}

const ONBOARDING_STEPS: OnboardingStep[] = [
  {
    id: 'welcome',
    title: 'Welcome to RAG Prompt Library',
    description: 'Take a quick tour to learn the basics',
    icon: PlayCircleIcon,
    action: 'Start Tour',
    completed: false
  },
  {
    id: 'create-prompt',
    title: 'Create Your First Prompt',
    description: 'Learn how to create reusable AI prompts',
    icon: DocumentTextIcon,
    action: 'Create Prompt',
    completed: false
  },
  {
    id: 'upload-document',
    title: 'Upload a Document',
    description: 'Enable RAG by uploading your first document',
    icon: DocumentTextIcon,
    action: 'Upload Document',
    completed: false,
    optional: true
  },
  {
    id: 'configure-settings',
    title: 'Configure Your Settings',
    description: 'Set up your API keys and preferences',
    icon: CogIcon,
    action: 'Open Settings',
    completed: false,
    optional: true
  }
];

interface GuidedOnboardingProps {
  isFirstTimeUser?: boolean;
  onComplete?: () => void;
}

export const GuidedOnboarding: React.FC<GuidedOnboardingProps> = ({
  isFirstTimeUser = false,
  onComplete
}) => {
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  const [onboardingData, setOnboardingData] = useState<OnboardingData>({
    currentStep: 0,
    completedSteps: [],
    skippedOnboarding: false,
    userId: currentUser?.uid
  });
  const [isVisible, setIsVisible] = useState(false);
  const { startTour } = useHelp();

  // Generate user-specific localStorage key
  const getStorageKey = () => {
    return currentUser ? `onboardingData_${currentUser.uid}` : 'onboardingData';
  };

  // Load onboarding state from localStorage
  useEffect(() => {
    if (!currentUser) return;

    const storageKey = getStorageKey();
    const savedData = localStorage.getItem(storageKey);

    if (savedData) {
      const parsed = JSON.parse(savedData);
      setOnboardingData({ ...parsed, userId: currentUser.uid });

      // Show onboarding if not completed and not skipped
      if (!parsed.skippedOnboarding && parsed.completedSteps.length < ONBOARDING_STEPS.filter(s => !s.optional).length) {
        setIsVisible(true);
      }
    } else if (isFirstTimeUser) {
      // First time user - show onboarding
      setIsVisible(true);
    }
  }, [currentUser, isFirstTimeUser]);

  // Save onboarding state to localStorage
  useEffect(() => {
    if (currentUser) {
      const storageKey = getStorageKey();
      localStorage.setItem(storageKey, JSON.stringify(onboardingData));
    }
  }, [onboardingData, currentUser]);

  const markStepCompleted = (stepId: string) => {
    setOnboardingData(prev => ({
      ...prev,
      completedSteps: [...prev.completedSteps, stepId],
      currentStep: Math.min(prev.currentStep + 1, ONBOARDING_STEPS.length - 1)
    }));
  };

  const skipOnboarding = () => {
    setOnboardingData(prev => ({
      ...prev,
      skippedOnboarding: true
    }));
    setIsVisible(false);
    onComplete?.();
  };

  const restartOnboarding = () => {
    setOnboardingData({
      currentStep: 0,
      completedSteps: [],
      skippedOnboarding: false
    });
    setIsVisible(true);
  };

  const handleStepAction = (step: OnboardingStep) => {
    switch (step.id) {
      case 'welcome':
        startTour('first-time-user');
        markStepCompleted(step.id);
        break;
      case 'create-prompt':
        // Navigate to prompt creation using React Router
        navigate('/prompts');
        markStepCompleted(step.id);
        break;
      case 'upload-document':
        // Navigate to document upload using React Router
        navigate('/documents');
        markStepCompleted(step.id);
        break;
      case 'configure-settings':
        // Navigate to settings using React Router
        navigate('/settings');
        markStepCompleted(step.id);
        break;
    }
  };

  const getStepStatus = (step: OnboardingStep, index: number) => {
    if (onboardingData.completedSteps.includes(step.id)) {
      return 'completed';
    }
    if (index === onboardingData.currentStep) {
      return 'current';
    }
    if (index < onboardingData.currentStep) {
      return 'completed';
    }
    return 'pending';
  };

  const completedRequiredSteps = ONBOARDING_STEPS
    .filter(step => !step.optional)
    .filter(step => onboardingData.completedSteps.includes(step.id)).length;

  const totalRequiredSteps = ONBOARDING_STEPS.filter(step => !step.optional).length;
  const isOnboardingComplete = completedRequiredSteps >= totalRequiredSteps;

  if (!isVisible && !isOnboardingComplete) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-20 right-4 bg-green-600 text-white px-4 py-2 rounded-lg shadow-lg hover:bg-green-700 transition-colors z-40"
      >
        Resume Onboarding
      </button>
    );
  }

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Getting Started</h2>
              <p className="text-gray-600 mt-1">
                Complete these steps to get the most out of RAG Prompt Library
              </p>
            </div>
            <div className="text-right">
              <div className="text-sm text-gray-500">Progress</div>
              <div className="text-lg font-semibold text-blue-600">
                {completedRequiredSteps}/{totalRequiredSteps}
              </div>
            </div>
          </div>
          
          {/* Progress Bar */}
          <div className="mt-4">
            <div className="bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${(completedRequiredSteps / totalRequiredSteps) * 100}%` }}
              />
            </div>
          </div>
        </div>

        {/* Steps */}
        <div className="p-6 space-y-4">
          {ONBOARDING_STEPS.map((step, index) => {
            const status = getStepStatus(step, index);
            const IconComponent = step.icon;

            return (
              <div
                key={step.id}
                className={`flex items-center p-4 rounded-lg border-2 transition-all ${
                  status === 'completed'
                    ? 'border-green-200 bg-green-50'
                    : status === 'current'
                    ? 'border-blue-200 bg-blue-50'
                    : 'border-gray-200 bg-gray-50'
                }`}
              >
                <div className="flex-shrink-0 mr-4">
                  {status === 'completed' ? (
                    <CheckCircleIcon className="h-8 w-8 text-green-600" />
                  ) : (
                    <IconComponent
                      className={`h-8 w-8 ${
                        status === 'current' ? 'text-blue-600' : 'text-gray-400'
                      }`}
                    />
                  )}
                </div>
                
                <div className="flex-grow">
                  <h3 className={`font-semibold ${
                    status === 'completed' ? 'text-green-900' :
                    status === 'current' ? 'text-blue-900' : 'text-gray-700'
                  }`}>
                    {step.title}
                    {step.optional && (
                      <span className="ml-2 text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded">
                        Optional
                      </span>
                    )}
                  </h3>
                  <p className={`text-sm ${
                    status === 'completed' ? 'text-green-700' :
                    status === 'current' ? 'text-blue-700' : 'text-gray-600'
                  }`}>
                    {step.description}
                  </p>
                </div>
                
                <div className="flex-shrink-0 ml-4">
                  {status === 'completed' ? (
                    <span className="text-green-600 font-medium">Completed</span>
                  ) : status === 'current' ? (
                    <button
                      onClick={() => handleStepAction(step)}
                      className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
                    >
                      {step.action}
                    </button>
                  ) : (
                    <span className="text-gray-400">Pending</span>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 bg-gray-50">
          {isOnboardingComplete ? (
            <div className="text-center">
              <div className="text-green-600 font-semibold mb-2">
                🎉 Congratulations! You've completed the onboarding.
              </div>
              <p className="text-gray-600 mb-4">
                You're all set to start creating amazing AI-powered content!
              </p>
              <button
                onClick={() => {
                  setIsVisible(false);
                  onComplete?.();
                }}
                className="bg-green-600 text-white px-6 py-2 rounded hover:bg-green-700 transition-colors"
              >
                Get Started
              </button>
            </div>
          ) : (
            <div className="flex items-center justify-between">
              <button
                onClick={skipOnboarding}
                className="text-gray-600 hover:text-gray-800 transition-colors"
              >
                Skip for now
              </button>
              
              <div className="flex space-x-3">
                <button
                  onClick={() => setIsVisible(false)}
                  className="px-4 py-2 border border-gray-300 rounded hover:bg-gray-50 transition-colors"
                >
                  Close
                </button>
                <button
                  onClick={() => {
                    const nextStep = ONBOARDING_STEPS[onboardingData.currentStep];
                    if (nextStep) {
                      handleStepAction(nextStep);
                    }
                  }}
                  className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
                  disabled={onboardingData.currentStep >= ONBOARDING_STEPS.length}
                >
                  Continue
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Onboarding trigger for settings page
export const OnboardingSettings: React.FC = () => {
  const [onboardingData, setOnboardingData] = useState<OnboardingData | null>(null);

  useEffect(() => {
    const savedData = localStorage.getItem('onboardingData');
    if (savedData) {
      setOnboardingData(JSON.parse(savedData));
    }
  }, []);

  const restartOnboarding = () => {
    const newData = {
      currentStep: 0,
      completedSteps: [],
      skippedOnboarding: false
    };
    localStorage.setItem('onboardingData', JSON.stringify(newData));
    window.location.reload();
  };

  if (!onboardingData) return null;

  return (
    <div className="bg-white p-6 rounded-lg border border-gray-200">
      <h3 className="text-lg font-semibold mb-4">Onboarding</h3>
      
      <div className="space-y-4">
        <div>
          <p className="text-gray-600 mb-2">
            Completed steps: {onboardingData.completedSteps.length}/{ONBOARDING_STEPS.length}
          </p>
          <div className="bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full"
              style={{ width: `${(onboardingData.completedSteps.length / ONBOARDING_STEPS.length) * 100}%` }}
            />
          </div>
        </div>
        
        <button
          onClick={restartOnboarding}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
        >
          Restart Onboarding
        </button>
      </div>
    </div>
  );
};

export default GuidedOnboarding;
