<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct Firebase Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .log { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; margin: 10px 0; font-family: monospace; font-size: 12px; max-height: 400px; overflow-y: auto; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <h1>🔥 Direct Firebase Connection Test</h1>
    <p>Testing direct connection to Firebase without the React app.</p>
    
    <button onclick="testFirebaseConnection()">Test Firebase Connection</button>
    <button onclick="testAuthentication()">Test Anonymous Auth</button>
    <button onclick="testFirestore()">Test Firestore</button>
    <button onclick="clearLog()">Clear Log</button>
    
    <div id="log" class="log">
        <div class="info">Ready to test Firebase connection...</div>
    </div>

    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getAuth, signInAnonymously, onAuthStateChanged } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
        import { getFirestore, collection, addDoc, getDocs, serverTimestamp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDJWjw2e8FayU3CvIWyGXXFAqDCTFN5CJs",
            authDomain: "rag-prompt-library.firebaseapp.com",
            projectId: "rag-prompt-library",
            storageBucket: "rag-prompt-library.firebasestorage.app",
            messagingSenderId: "743998930129",
            appId: "1:743998930129:web:69dd61394ed81598cd99f0",
            measurementId: "G-CEDFF0WMPW"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        // Logging function
        function log(level, message, data = null) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = level.toLowerCase();
            
            let logEntry = `<div class="${className}">[${timestamp}] [${level.toUpperCase()}] ${message}`;
            if (data) {
                logEntry += `<br><pre>${JSON.stringify(data, null, 2)}</pre>`;
            }
            logEntry += '</div>';
            
            logDiv.innerHTML += logEntry;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        // Test functions
        window.testFirebaseConnection = function() {
            log('INFO', 'Testing Firebase connection...');
            try {
                log('SUCCESS', 'Firebase app initialized successfully');
                log('INFO', 'Project ID: ' + app.options.projectId);
                log('INFO', 'Auth Domain: ' + app.options.authDomain);
                return true;
            } catch (error) {
                log('ERROR', 'Firebase connection failed', error);
                return false;
            }
        };

        window.testAuthentication = async function() {
            log('INFO', 'Testing anonymous authentication...');
            try {
                const result = await signInAnonymously(auth);
                log('SUCCESS', 'Anonymous authentication successful');
                log('INFO', 'User ID: ' + result.user.uid);
                return result.user;
            } catch (error) {
                log('ERROR', 'Authentication failed', error);
                return null;
            }
        };

        window.testFirestore = async function() {
            log('INFO', 'Testing Firestore connection...');
            
            // First ensure we're authenticated
            if (!auth.currentUser) {
                log('WARNING', 'No authenticated user, attempting anonymous sign-in...');
                const user = await window.testAuthentication();
                if (!user) {
                    log('ERROR', 'Cannot test Firestore without authentication');
                    return false;
                }
            }

            try {
                const userId = auth.currentUser.uid;
                log('INFO', 'Testing with user ID: ' + userId);

                // Test reading
                const promptsRef = collection(db, 'users', userId, 'prompts');
                log('INFO', 'Created collection reference: ' + promptsRef.path);

                const querySnapshot = await getDocs(promptsRef);
                log('SUCCESS', 'Successfully read prompts collection');
                log('INFO', 'Existing prompts count: ' + querySnapshot.size);

                // Test writing
                const testPrompt = {
                    title: 'Direct Firebase Test Prompt',
                    content: 'This is a test prompt created directly via Firebase SDK.',
                    description: 'Test prompt for debugging',
                    category: 'General',
                    tags: ['test', 'debug', 'direct'],
                    isPublic: false,
                    variables: [],
                    createdBy: userId,
                    createdAt: serverTimestamp(),
                    updatedAt: serverTimestamp(),
                    version: 1
                };

                log('INFO', 'Attempting to create test prompt...');
                const docRef = await addDoc(promptsRef, testPrompt);
                log('SUCCESS', 'Test prompt created successfully!');
                log('INFO', 'Document ID: ' + docRef.id);

                return true;
            } catch (error) {
                log('ERROR', 'Firestore test failed', error);
                return false;
            }
        };

        window.clearLog = function() {
            document.getElementById('log').innerHTML = '<div class="info">Log cleared...</div>';
        };

        // Monitor auth state
        onAuthStateChanged(auth, (user) => {
            if (user) {
                log('INFO', 'User authenticated: ' + user.uid);
            } else {
                log('INFO', 'No user authenticated');
            }
        });

        log('INFO', 'Firebase SDK loaded and ready for testing');
    </script>
</body>
</html>
