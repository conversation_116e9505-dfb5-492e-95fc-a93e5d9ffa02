# Execute Prompt Button Troubleshooting Guide

## 🔧 Quick Diagnostic Steps

### Step 1: Run the Diagnostic Script
1. Open your browser's Developer Tools (F12)
2. Go to the Console tab
3. Copy and paste the contents of `execute-prompt-diagnostics.js` into the console
4. Run `diagnostics.runAll()` to get a comprehensive report

### Step 2: Check Browser Console for Errors
Look for any red error messages in the console, particularly:
- Firebase initialization errors
- Authentication errors
- Network/CORS errors
- Function call errors

## 🔍 Common Issues and Solutions

### Issue 1: But<PERSON> is Disabled
**Symptoms:** Execute Prompt button appears grayed out and unclickable

**Possible Causes:**
- Required fields not filled
- User not authenticated
- Validation errors present

**Solutions:**
1. **Check Required Fields:**
   ```javascript
   // Run in console to check for empty required fields
   diagnostics.checkValidationErrors()
   ```

2. **Verify Authentication:**
   ```javascript
   // Check if user is signed in
   diagnostics.testAuthentication()
   ```

3. **Look for Validation Errors:**
   - Check for red error messages near input fields
   - Ensure all required variables have values
   - Verify prompt content is not empty

### Issue 2: Button Click Does Nothing
**Symptoms:** Button is clickable but nothing happens when clicked

**Possible Causes:**
- JavaScript errors preventing execution
- Firebase Functions not responding
- Network connectivity issues

**Solutions:**
1. **Check Console for JavaScript Errors:**
   - Look for any red error messages
   - Check if Firebase is properly initialized

2. **Test Firebase Functions:**
   ```javascript
   // Test if functions are working
   diagnostics.testFirebaseFunctions()
   ```

3. **Test Network Connectivity:**
   ```javascript
   // Check basic network connectivity
   diagnostics.testNetworkConnectivity()
   ```

### Issue 3: Authentication Errors
**Symptoms:** "User must be authenticated" or similar auth errors

**Solutions:**
1. **Sign Out and Sign Back In:**
   - Click your profile menu
   - Sign out completely
   - Sign back in with your account

2. **Clear Browser Data:**
   - Clear cookies and local storage for the site
   - Refresh the page
   - Sign in again

3. **Check Firebase Auth Configuration:**
   - Verify environment variables are set correctly
   - Check if Firebase Auth is properly initialized

### Issue 4: Firebase Functions Errors
**Symptoms:** Function call errors, timeout errors, or "functions not available"

**Solutions:**
1. **Verify Functions Deployment:**
   ```bash
   # In your project directory
   cd functions
   firebase deploy --only functions
   ```

2. **Check Function Logs:**
   ```bash
   firebase functions:log
   ```

3. **Test Function Endpoints:**
   ```javascript
   // Test health endpoint
   diagnostics.testFirebaseFunctions()
   ```

### Issue 5: OpenRouter API Errors
**Symptoms:** AI model errors, API key errors, or timeout errors

**Solutions:**
1. **Test OpenRouter Connection:**
   ```javascript
   // Test OpenRouter API
   diagnostics.testOpenRouterAPI()
   ```

2. **Verify API Key Configuration:**
   - Check that `OPENROUTER_API_KEY` is set in Firebase Functions environment
   - Verify the API key is valid and has sufficient credits

3. **Check OpenRouter Service Status:**
   - Visit https://openrouter.ai/status
   - Try a different model if current one is unavailable

### Issue 6: Firestore Permission Errors
**Symptoms:** "Permission denied" or "Firestore access failed"

**Solutions:**
1. **Check Firestore Rules:**
   - Verify user has read/write access to their prompts
   - Check if rules allow authenticated users

2. **Test Firestore Access:**
   ```javascript
   // Test Firestore connectivity
   diagnostics.testFirestoreAccess()
   ```

## 🚀 Step-by-Step Debugging Process

### 1. Initial Checks
- [ ] User is signed in
- [ ] On correct page (`/prompts/{id}/execute`)
- [ ] All required fields are filled
- [ ] No validation errors visible

### 2. Browser Console Checks
- [ ] No JavaScript errors in console
- [ ] Firebase initialized successfully
- [ ] No network errors

### 3. Firebase Services Checks
- [ ] Authentication working
- [ ] Functions responding
- [ ] Firestore accessible
- [ ] OpenRouter API working

### 4. Advanced Debugging
- [ ] Check Network tab for failed requests
- [ ] Verify request payload is correct
- [ ] Check response data structure
- [ ] Test with different browsers

## 🔧 Quick Fixes to Try

1. **Refresh and Retry:**
   - Hard refresh the page (Ctrl+F5)
   - Try clicking the button again

2. **Clear Browser Cache:**
   - Clear site data and cookies
   - Disable browser extensions temporarily

3. **Try Different Browser:**
   - Test in incognito/private mode
   - Try Chrome, Firefox, or Edge

4. **Check Required Fields:**
   - Ensure all prompt variables are filled
   - Verify prompt content is not empty

5. **Test Connection:**
   - Click "Test Connection" button first
   - Verify OpenRouter API is working

## 📊 Using the Diagnostic Tools

### HTML Debug Tool
1. Open `debug-execute-prompt-comprehensive.html` in your browser
2. Configure test parameters
3. Run comprehensive tests
4. Follow the recommendations provided

### Console Diagnostic Script
1. Copy `execute-prompt-diagnostics.js` content
2. Paste in browser console
3. Run `diagnostics.runAll()`
4. Check the detailed report

### Manual Button Test
```javascript
// Try clicking the button programmatically
diagnostics.clickExecuteButton()
```

## 🆘 When to Seek Further Help

Contact support if:
- All diagnostic tests pass but button still doesn't work
- Consistent authentication failures
- Firebase Functions deployment issues
- OpenRouter API consistently failing

## 📝 Information to Provide When Reporting Issues

1. **Browser and Version:** (e.g., Chrome 120.0.6099.109)
2. **Operating System:** (e.g., Windows 11, macOS 14.1)
3. **Console Error Messages:** (copy exact error text)
4. **Diagnostic Test Results:** (output from `diagnostics.runAll()`)
5. **Steps to Reproduce:** (what you clicked, what happened)
6. **Expected vs Actual Behavior:** (what should happen vs what actually happens)

## 🔄 Recovery Steps

If the Execute Prompt functionality is completely broken:

1. **Reset Local State:**
   ```javascript
   // Clear all local storage
   localStorage.clear();
   sessionStorage.clear();
   location.reload();
   ```

2. **Redeploy Functions:**
   ```bash
   cd functions
   npm install
   firebase deploy --only functions
   ```

3. **Check Environment Variables:**
   - Verify all required environment variables are set
   - Ensure API keys are valid and active

4. **Test with Minimal Prompt:**
   - Create a simple prompt with no variables
   - Try executing it to isolate the issue

Remember: Most Execute Prompt issues are related to authentication, validation, or network connectivity. The diagnostic tools will help identify the specific problem quickly.
