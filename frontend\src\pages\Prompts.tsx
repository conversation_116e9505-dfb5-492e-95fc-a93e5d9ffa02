import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { promptService } from '../services/firestore';
import type { Prompt } from '../types';
import { AIEnhancedPromptEditor } from '../components/prompts/AIEnhancedPromptEditor';
import { PromptList } from '../components/prompts/PromptList';
import { Button } from '../components/common/Button';
import { ErrorHandler, useErrorHandler } from '../components/common/ErrorHandler';
import { Plus, ArrowLeft, Sparkles } from 'lucide-react';

type ViewMode = 'list' | 'create' | 'create-ai' | 'create-scratch' | 'edit';

export const Prompts: React.FC = () => {
  const { currentUser } = useAuth();
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [selectedPrompt, setSelectedPrompt] = useState<Prompt | undefined>();
  const [loading, setLoading] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const { error, handleError, clearError, retryWithErrorHandling } = useErrorHandler();

  const handleCreatePromptFromScratch = () => {
    setSelectedPrompt(undefined);
    setViewMode('create-scratch');
  };

  const handleCreatePromptWithAI = () => {
    setSelectedPrompt(undefined);
    setViewMode('create-ai');
  };

  const handleEditPrompt = (prompt: Prompt) => {
    setSelectedPrompt(prompt);
    setViewMode('edit');
  };



  const handleSavePrompt = async (promptData: Partial<Prompt>) => {
    // Enhanced authentication check with retry mechanism
    const waitForAuth = async (maxWaitMs = 5000): Promise<boolean> => {
      const startTime = Date.now();
      while (Date.now() - startTime < maxWaitMs) {
        if (currentUser && currentUser.uid) {
          return true;
        }
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      return false;
    };

    const isAuthenticated = await waitForAuth();
    if (!isAuthenticated) {
      console.error('❌ Authentication timeout - no current user found after waiting');
      handleError(new Error('Authentication required. Please sign in and try again.'));
      return;
    }

    await retryWithErrorHandling(async () => {
      console.log('🚀 Starting prompt save process...');
      console.log('👤 Current user:', currentUser!.uid);
      console.log('📝 Prompt data to save:', promptData);
      console.log('🔄 View mode:', viewMode);

      // Validate prompt data before saving
      if (!promptData.title?.trim()) {
        throw new Error('Prompt title is required');
      }
      if (!promptData.content?.trim()) {
        throw new Error('Prompt content is required');
      }

      setLoading(true);

      try {
        let result: string | void;

        if (viewMode === 'create' || viewMode === 'create-ai' || viewMode === 'create-scratch') {
          console.log('➕ Creating new prompt...');
          result = await promptService.createPrompt(currentUser!.uid, promptData as Omit<Prompt, 'id' | 'createdAt' | 'updatedAt' | 'createdBy' | 'version'>);
          console.log('✅ Prompt created with ID:', result);
        } else if (viewMode === 'edit' && selectedPrompt) {
          console.log('✏️ Updating existing prompt:', selectedPrompt.id);
          await promptService.updatePrompt(currentUser!.uid, selectedPrompt.id, promptData);
          console.log('✅ Prompt updated successfully');
        }

        // Verify the save was successful by checking if the prompt exists
        if (result && typeof result === 'string') {
          const savedPrompt = await promptService.getPrompt(currentUser!.uid, result);
          if (!savedPrompt) {
            throw new Error('Prompt was saved but could not be retrieved. Please refresh the page.');
          }
          console.log('✅ Prompt save verified:', savedPrompt.title);
        }

        console.log('🔄 Returning to prompt list (real-time sync will update automatically)...');
        setViewMode('list');
        setSelectedPrompt(undefined);
        // No need for manual refresh - real-time sync will handle updates

        console.log('🎉 Prompt save process completed successfully!');
        // Clear any previous errors on success
        clearError();
      } finally {
        setLoading(false);
      }
    });
  };

  const handleExecuteFromEditor = async (promptData: Partial<Prompt>) => {
    // TODO: Implement prompt execution from editor
    console.log('Executing prompt from editor:', promptData);
    alert('Prompt execution will be implemented in the next phase!');
  };

  const handleBackToList = () => {
    setViewMode('list');
    setSelectedPrompt(undefined);
  };

  return (
    <div>
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            {viewMode !== 'list' && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBackToList}
                className="mr-4"
              >
                <ArrowLeft className="w-4 h-4 mr-1" />
                Back
              </Button>
            )}
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                {viewMode === 'list' && 'Prompts'}
                {viewMode === 'create' && 'Create New Prompt'}
                {viewMode === 'edit' && 'Edit Prompt'}
              </h1>
              <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                {viewMode === 'list' && 'Manage your AI prompts and templates'}
                {viewMode === 'create' && 'Create a new prompt template'}
                {viewMode === 'edit' && 'Edit your prompt template'}
              </p>
            </div>
          </div>

          {viewMode === 'list' && (
            <div className="flex items-center space-x-3">
              <Button
                variant="outline"
                onClick={handleCreatePromptFromScratch}
                data-help="create-prompt-button"
                className="flex items-center"
              >
                <Plus className="w-4 h-4 mr-2" />
                Create from Scratch
              </Button>
              <Button
                variant="primary"
                onClick={handleCreatePromptWithAI}
                data-help="ai-create-prompt-button"
                className="flex items-center"
              >
                <Sparkles className="w-4 h-4 mr-2" />
                AI-Assisted Creation
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Content */}
      {viewMode === 'list' && (
        <PromptList
          onEditPrompt={handleEditPrompt}
          refreshTrigger={refreshTrigger}
        />
      )}

      {(viewMode === 'create' || viewMode === 'create-ai' || viewMode === 'create-scratch' || viewMode === 'edit') && (
        <AIEnhancedPromptEditor
          prompt={selectedPrompt}
          onSave={handleSavePrompt}
          onExecute={handleExecuteFromEditor}
          loading={loading}
          initialMode={viewMode === 'create-ai' ? 'wizard' : viewMode === 'create-scratch' ? 'edit' : undefined}
        />
      )}
    </div>
  );
};
