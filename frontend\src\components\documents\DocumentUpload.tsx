import React, { useState, useRef, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { Upload, File, X, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import { Button } from '../common/Button';
import { useSuccessToast, useErrorToast } from '../common/Toast';
import { ref, uploadBytesResumable, getDownloadURL } from 'firebase/storage';
import { collection, addDoc, serverTimestamp } from 'firebase/firestore';
import { storage, db } from '../../config/firebase';
import { LoadingSpinner } from '../common/LoadingSpinner';
import { documentDebugger, isJobLikelyStuck } from '../../utils/documentDebugger';

interface UploadFile {
  file: File;
  id: string;
  status: 'pending' | 'uploading' | 'processing' | 'extracting' | 'chunking' | 'embedding' | 'indexing' | 'completed' | 'error';
  progress: number;
  error?: string;
  documentId?: string;
  jobId?: string;
  uploadStartTime?: Date;
  uploadEndTime?: Date;
  processingStartTime?: Date;
  processingEndTime?: Date;
  retryCount?: number;
}

interface DocumentUploadProps {
  onUploadComplete?: (documentId: string, filename: string) => void;
  onUploadStart?: (filename: string) => void;
  onUploadProgress?: (filename: string, progress: number, status: string) => void;
  onUploadError?: (filename: string, error: string) => void;
  maxFiles?: number;
  maxFileSize?: number; // in MB
  acceptedTypes?: string[];
  className?: string;
  autoUpload?: boolean;
  showDetailedProgress?: boolean;
  enableRetry?: boolean;
  maxRetries?: number;
}

export const DocumentUpload: React.FC<DocumentUploadProps> = ({
  onUploadComplete,
  onUploadStart,
  onUploadProgress,
  onUploadError,
  maxFiles = 10,
  maxFileSize = 50,
  acceptedTypes = ['.pdf', '.docx', '.txt', '.md'],
  className = '',
  autoUpload = false,
  showDetailedProgress = true,
  enableRetry = true,
  maxRetries = 3
}) => {
  const { currentUser } = useAuth();
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const activePollingRef = useRef<Set<string>>(new Set()); // Track active polling operations
  const successToast = useSuccessToast();
  const errorToast = useErrorToast();

  // Cleanup polling operations on unmount
  useEffect(() => {
    return () => {
      activePollingRef.current.clear();
    };
  }, []);

  const acceptedTypesMap = [
    'application/pdf',
    'text/plain',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/markdown'
  ];

  const acceptedExtensions = ['.pdf', '.txt', '.doc', '.docx', '.md'];

  const maxFileSizeBytes = maxFileSize * 1024 * 1024; // Convert MB to bytes

  const validateFileType = (file: File): boolean => {
    // Get file extension
    const fileName = file.name.toLowerCase();
    const extension = '.' + fileName.split('.').pop();

    // Check if extension is supported
    if (!acceptedExtensions.includes(extension)) {
      return false;
    }

    // For .md files, browsers may not set the correct MIME type
    // So we'll accept them based on extension
    if (extension === '.md') {
      return true;
    }

    // For other files, check MIME type as well
    return acceptedTypesMap.includes(file.type);
  };

  const handleFileSelect = (files: FileList | null) => {
    if (!files || !currentUser) return;

    const newFiles: UploadFile[] = [];
    const errors: string[] = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];

      // Validate file type
      if (!validateFileType(file)) {
        errors.push(`Unsupported file type: ${file.name}`);
        continue;
      }

      // Validate file size
      if (file.size > maxFileSizeBytes) {
        errors.push(`File too large: ${file.name} (max ${maxFileSize}MB)`);
        continue;
      }

      newFiles.push({
        file,
        id: `${Date.now()}-${i}`,
        status: 'pending',
        progress: 0
      });
    }

    setValidationErrors(errors);
    setUploadFiles(prev => [...prev, ...newFiles]);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const removeFile = (id: string) => {
    setUploadFiles(prev => prev.filter(f => f.id !== id));
  };

  const uploadFile = async (uploadFile: UploadFile) => {
    if (!currentUser) return;

    try {
      // Update status to uploading
      setUploadFiles(prev => prev.map(f =>
        f.id === uploadFile.id ? { ...f, status: 'uploading' as const } : f
      ));

      // Create unique file path
      const timestamp = Date.now();
      const fileName = `${timestamp}_${uploadFile.file.name}`;
      const filePath = `documents/${currentUser.uid}/${fileName}`;

      // Create storage reference
      const storageRef = ref(storage, filePath);

      // Start upload with progress tracking
      const uploadTask = uploadBytesResumable(storageRef, uploadFile.file);

      // Track upload progress
      uploadTask.on('state_changed',
        (snapshot) => {
          const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
          setUploadFiles(prev => prev.map(f =>
            f.id === uploadFile.id ? { ...f, progress: Math.round(progress) } : f
          ));
        },
        (error) => {
          console.error('Upload error:', error);
          setUploadFiles(prev => prev.map(f =>
            f.id === uploadFile.id ? {
              ...f,
              status: 'error' as const,
              error: error.message || 'Upload failed'
            } : f
          ));

          // Show error toast
          errorToast(
            'Upload failed',
            `Failed to upload ${uploadFile.file.name}: ${error.message || 'Unknown error'}`
          );
        },
        async () => {
          try {
            // Upload completed successfully
            const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);

            // Update status to processing
            setUploadFiles(prev => prev.map(f =>
              f.id === uploadFile.id ? { ...f, status: 'processing' as const } : f
            ));

            // Create document metadata in Firestore
            const docRef = await addDoc(collection(db, 'rag_documents'), {
              filename: uploadFile.file.name,
              originalName: uploadFile.file.name,
              filePath: filePath,
              downloadURL: downloadURL,
              uploadedBy: currentUser.uid,
              uploadedAt: serverTimestamp(),
              size: uploadFile.file.size,
              type: uploadFile.file.type,
              status: 'uploaded',
              processingStartedAt: null,
              processedAt: null,
              chunks: [],
              metadata: {
                originalSize: uploadFile.file.size,
                contentType: uploadFile.file.type
              }
            });

            // Update status to processing and start polling
            setUploadFiles(prev => prev.map(f =>
              f.id === uploadFile.id ? {
                ...f,
                status: 'processing' as const,
                documentId: docRef.id,
                jobId: docRef.id, // Use document ID as job ID for now
                uploadEndTime: new Date(),
                processingStartTime: new Date(),
                progress: 25
              } : f
            ));

            // Call onUploadStart callback
            if (onUploadStart) {
              onUploadStart(uploadFile.file.name);
            }

            // Start polling for processing status
            pollProcessingStatus(uploadFile.id, docRef.id);

          } catch (firestoreError) {
            console.error('Firestore error:', firestoreError);
            setUploadFiles(prev => prev.map(f =>
              f.id === uploadFile.id ? {
                ...f,
                status: 'error' as const,
                error: 'Failed to save document metadata'
              } : f
            ));
          }
        }
      );

    } catch (error) {
      console.error('Upload initialization error:', error);
      setUploadFiles(prev => prev.map(f =>
        f.id === uploadFile.id ? {
          ...f,
          status: 'error' as const,
          error: error instanceof Error ? error.message : 'Upload failed'
        } : f
      ));
    }
  };

  const pollProcessingStatus = async (fileId: string, jobId: string) => {
    const maxAttempts = 60; // 5 minutes with 5-second intervals
    let attempts = 0;

    // Add to active polling set
    activePollingRef.current.add(jobId);

    // Start debugging
    documentDebugger.startDebugging(jobId);

    const poll = async () => {
      // Check if polling should continue
      if (!activePollingRef.current.has(jobId)) {
        console.log(`Polling stopped for job ${jobId} (component unmounted or cancelled)`);
        documentDebugger.stopDebugging(jobId);
        return;
      }

      try {
        const response = await fetch(`/api/ai/document-status/${jobId}`);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        // Log polling attempt for debugging
        documentDebugger.logPollAttempt(jobId, data);

        // Check if API call was successful
        if (!data.success) {
          throw new Error(data.error || 'API call failed');
        }

        console.log(`📊 Poll attempt ${attempts + 1}/${maxAttempts} for job ${jobId}: ${data.status}`);

        if (data.status === 'completed') {
          // Remove from active polling
          activePollingRef.current.delete(jobId);
          documentDebugger.stopDebugging(jobId);

          setUploadFiles(prev => prev.map(f =>
            f.id === fileId
              ? { ...f, status: 'completed', progress: 100, processingEndTime: new Date() }
              : f
          ));

          const file = uploadFiles.find(f => f.id === fileId);
          if (file && onUploadComplete) {
            onUploadComplete(data.document_id, file.file.name);
          }

          successToast('Document processed successfully!');

        } else if (data.status === 'failed') {
          // Remove from active polling
          activePollingRef.current.delete(jobId);
          documentDebugger.stopDebugging(jobId);

          setUploadFiles(prev => prev.map(f =>
            f.id === fileId
              ? {
                  ...f,
                  status: 'error',
                  error: data.error_message || 'Processing failed'
                }
              : f
          ));

          if (onUploadError) {
            const file = uploadFiles.find(f => f.id === fileId);
            onUploadError(file?.file.name || 'Unknown file', data.error_message || 'Processing failed');
          }

        } else if (attempts < maxAttempts) {
          // Check if job appears to be stuck
          if (attempts > 10 && isJobLikelyStuck(jobId)) {
            console.warn(`Job ${jobId} appears to be stuck. Debug report:`);
            console.warn(documentDebugger.generateDebugReport(jobId));
          }

          // Update progress based on processing steps
          const progressMap = {
            'extracting': 25,
            'chunking': 50,
            'embedding': 75,
            'indexing': 90
          };

          const progress = progressMap[data.status as keyof typeof progressMap] || 50;

          setUploadFiles(prev => prev.map(f =>
            f.id === fileId
              ? { ...f, progress, status: data.status as UploadFile['status'] }
              : f
          ));

          if (onUploadProgress) {
            const file = uploadFiles.find(f => f.id === fileId);
            onUploadProgress(file?.file.name || 'Unknown file', progress, data.status);
          }

          attempts++;
          setTimeout(poll, 5000); // Poll every 5 seconds
        } else {
          // Timeout - stop polling after maxAttempts
          activePollingRef.current.delete(jobId);
          console.warn(`⏰ Document processing timeout for job ${jobId} after ${maxAttempts} attempts`);
          console.warn('Final debug report:', documentDebugger.generateDebugReport(jobId));
          documentDebugger.stopDebugging(jobId);

          setUploadFiles(prev => prev.map(f =>
            f.id === fileId
              ? {
                  ...f,
                  status: 'error',
                  error: 'Processing timeout - please try uploading again'
                }
              : f
          ));

          if (onUploadError) {
            const file = uploadFiles.find(f => f.id === fileId);
            onUploadError(file?.file.name || 'Unknown file', 'Processing timeout');
          }
        }

      } catch (error) {
        // Remove from active polling on error
        activePollingRef.current.delete(jobId);
        documentDebugger.logError(jobId, (error as Error).message);
        console.error('❌ Status polling error:', error);
        console.error('Debug report:', documentDebugger.generateDebugReport(jobId));
        documentDebugger.stopDebugging(jobId);

        setUploadFiles(prev => prev.map(f =>
          f.id === fileId
            ? {
                ...f,
                status: 'error',
                error: 'Failed to check processing status'
              }
            : f
        ));

        if (onUploadError) {
          const file = uploadFiles.find(f => f.id === fileId);
          onUploadError(file?.file.name || 'Unknown file', 'Failed to check processing status');
        }
      }
    };

    poll();
  };

  const uploadAllFiles = async () => {
    const pendingFiles = uploadFiles.filter(f => f.status === 'pending');

    for (const file of pendingFiles) {
      await uploadFile(file);
    }
  };

  const getStatusIcon = (status: UploadFile['status']) => {
    switch (status) {
      case 'pending':
        return <File className="w-5 h-5 text-gray-400" />;
      case 'uploading':
      case 'processing':
      case 'extracting':
      case 'chunking':
      case 'embedding':
      case 'indexing':
        return <Loader2 className="w-5 h-5 animate-spin text-blue-500" />;
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
    }
  };

  const getStatusText = (status: UploadFile['status']) => {
    switch (status) {
      case 'pending':
        return 'Ready to upload';
      case 'uploading':
        return 'Uploading...';
      case 'processing':
        return 'Processing...';
      case 'extracting':
        return 'Extracting text...';
      case 'chunking':
        return 'Creating chunks...';
      case 'embedding':
        return 'Generating embeddings...';
      case 'indexing':
        return 'Indexing vectors...';
      case 'completed':
        return 'Completed';
      case 'error':
        return 'Error';
    }
  };

  return (
    <div className="space-y-6">
      {/* Upload Area */}
      <div
        className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
          isDragOver
            ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/20'
            : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
        }`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Upload Documents
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
          Drag and drop files here, or click to select files
        </p>
        <p className="text-xs text-gray-500 dark:text-gray-400 mb-4">
          Supported formats: PDF, TXT, DOC, DOCX, MD (max 10MB each)
        </p>
        <Button
          variant="outline"
          onClick={() => fileInputRef.current?.click()}
          aria-label="Upload documents - Select files to upload"
        >
          Select Files
        </Button>
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept=".pdf,.txt,.doc,.docx,.md"
          onChange={(e) => handleFileSelect(e.target.files)}
          className="hidden"
          aria-label="File input for document upload"
        />
      </div>

      {/* Validation Errors */}
      {validationErrors.length > 0 && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-red-400 mr-2 mt-0.5" />
            <div>
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                Upload Errors
              </h3>
              <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                <ul className="list-disc pl-5 space-y-1">
                  {validationErrors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* File List */}
      {uploadFiles.length > 0 && (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Files ({uploadFiles.length})
              </h3>
              {uploadFiles.some(f => f.status === 'pending') && (
                <Button
                  variant="primary"
                  size="sm"
                  onClick={uploadAllFiles}
                >
                  Upload All
                </Button>
              )}
            </div>

            <div className="space-y-3">
              {uploadFiles.map((fileItem) => (
                <div
                  key={fileItem.id}
                  className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-600 rounded-md"
                >
                  <div className="flex items-center space-x-3 flex-1">
                    {getStatusIcon(fileItem.status)}
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {fileItem.file.name}
                      </p>
                      <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                        <span>{(fileItem.file.size / 1024 / 1024).toFixed(2)} MB</span>
                        <span>•</span>
                        <span>{getStatusText(fileItem.status)}</span>
                        {fileItem.error && (
                          <>
                            <span>•</span>
                            <span className="text-red-500">{fileItem.error}</span>
                          </>
                        )}
                      </div>
                      {(fileItem.status === 'uploading' ||
                        fileItem.status === 'processing' ||
                        fileItem.status === 'extracting' ||
                        fileItem.status === 'chunking' ||
                        fileItem.status === 'embedding' ||
                        fileItem.status === 'indexing') && (
                        <div className="mt-1 w-full bg-gray-200 rounded-full h-1.5 dark:bg-gray-700">
                          <div
                            className="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
                            style={{ width: `${fileItem.progress}%` }}
                          />
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    {fileItem.status === 'pending' && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => uploadFile(fileItem)}
                      >
                        Upload
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFile(fileItem.id)}
                      disabled={fileItem.status === 'uploading' || fileItem.status === 'processing'}
                      aria-label={`Remove ${fileItem.file.name}`}
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
