<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .processing { background-color: #fff3cd; }
        .completed { background-color: #d4edda; }
        .error { background-color: #f8d7da; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-secondary { background-color: #6c757d; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>Document Upload Spinning Icon Test</h1>
    
    <div class="test-section">
        <h2>Test Scenario: Infinite Spinning Icon</h2>
        <p>This test simulates the issue where the processing icon keeps spinning indefinitely.</p>
        
        <div id="document-status" class="status processing">
            <span class="spinner"></span>
            <span>Processing document... (0 documents)</span>
        </div>
        
        <button class="btn-primary" onclick="simulateUpload()">Simulate Document Upload</button>
        <button class="btn-secondary" onclick="simulateCompletion()">Force Complete</button>
        <button class="btn-danger" onclick="simulateError()">Force Error</button>
        <button class="btn-secondary" onclick="resetTest()">Reset</button>
    </div>

    <div class="test-section">
        <h2>API Status Polling Test</h2>
        <p>Test the actual API endpoint that might be causing the issue:</p>
        
        <input type="text" id="jobId" placeholder="Enter Job ID" style="width: 300px; padding: 8px;">
        <button class="btn-primary" onclick="testAPIPolling()">Test API Polling</button>
        <button class="btn-danger" onclick="stopPolling()">Stop Polling</button>
        
        <div id="api-log" class="log"></div>
    </div>

    <div class="test-section">
        <h2>Potential Issues Identified</h2>
        <ul>
            <li><strong>API Response Structure Mismatch:</strong> Frontend expects <code>data.status</code> but API might return different structure</li>
            <li><strong>Status Value Mismatch:</strong> Backend returns enum values that don't match frontend expectations</li>
            <li><strong>Polling Never Stops:</strong> No proper cleanup when component unmounts or errors occur</li>
            <li><strong>Network Errors:</strong> Failed API calls don't stop the polling loop</li>
            <li><strong>Backend Processing Stuck:</strong> Document processing pipeline might be stuck in intermediate state</li>
        </ul>
    </div>

    <script>
        let pollingInterval = null;
        let pollingAttempts = 0;
        const maxAttempts = 60;

        function log(message) {
            const logElement = document.getElementById('api-log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function updateStatus(status, message, isSpinning = false) {
            const statusElement = document.getElementById('document-status');
            statusElement.className = `status ${status}`;
            
            const spinnerHtml = isSpinning ? '<span class="spinner"></span> ' : '';
            statusElement.innerHTML = `${spinnerHtml}<span>${message}</span>`;
        }

        function simulateUpload() {
            updateStatus('processing', 'Processing document... (Simulated)', true);
            log('Simulated document upload started');
            
            // Simulate the infinite spinning issue
            pollingAttempts = 0;
            simulatePolling();
        }

        function simulatePolling() {
            if (pollingAttempts >= maxAttempts) {
                updateStatus('error', 'Processing timeout - polling stopped after 60 attempts', false);
                log('ERROR: Polling timeout after 60 attempts');
                return;
            }

            pollingAttempts++;
            log(`Polling attempt ${pollingAttempts}/${maxAttempts} - Status: processing`);
            
            // Simulate the issue where status never changes from 'processing'
            setTimeout(simulatePolling, 5000); // Poll every 5 seconds
        }

        function simulateCompletion() {
            updateStatus('completed', 'Document processed successfully!', false);
            log('Document processing completed (forced)');
        }

        function simulateError() {
            updateStatus('error', 'Document processing failed', false);
            log('Document processing failed (forced)');
        }

        function resetTest() {
            updateStatus('processing', 'Ready to test...', false);
            document.getElementById('api-log').innerHTML = '';
            pollingAttempts = 0;
            log('Test reset');
        }

        async function testAPIPolling() {
            const jobId = document.getElementById('jobId').value.trim();
            if (!jobId) {
                log('ERROR: Please enter a Job ID');
                return;
            }

            log(`Starting API polling for job: ${jobId}`);
            pollingAttempts = 0;
            
            if (pollingInterval) {
                clearInterval(pollingInterval);
            }

            pollingInterval = setInterval(async () => {
                if (pollingAttempts >= maxAttempts) {
                    log('ERROR: Polling timeout after 60 attempts');
                    stopPolling();
                    return;
                }

                pollingAttempts++;
                
                try {
                    log(`API Poll attempt ${pollingAttempts}/${maxAttempts} for job ${jobId}`);
                    
                    const response = await fetch(`/api/ai/document-status/${jobId}`);
                    const data = await response.json();
                    
                    log(`Response: ${JSON.stringify(data, null, 2)}`);
                    
                    if (data.success && (data.status === 'completed' || data.status === 'failed')) {
                        log(`Job ${jobId} finished with status: ${data.status}`);
                        stopPolling();
                    }
                    
                } catch (error) {
                    log(`ERROR: ${error.message}`);
                }
            }, 5000);
        }

        function stopPolling() {
            if (pollingInterval) {
                clearInterval(pollingInterval);
                pollingInterval = null;
                log('Polling stopped');
            }
        }

        // Initialize
        resetTest();
    </script>
</body>
</html>
