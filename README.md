# RAG Prompt Library - Smart, Modular, RAG-Enabled Prompt Management System

A modern, Firebase-powered platform for managing AI prompts with integrated Retrieval-Augmented Generation (RAG) capabilities. Built with React 18, TypeScript, and enterprise-grade Firebase infrastructure.

## 🎯 **Current Status: Foundation Deployed**
- ✅ **Frontend Deployed**: Live at [react-app-000730.web.app](https://react-app-000730.web.app)
- ✅ **Firebase Functions**: Basic API endpoints operational
- ✅ **Authentication**: Firebase Auth with Google OAuth working
- 🚧 **AI Integration**: Basic framework in place, full features in development
- 🚧 **RAG Pipeline**: Architecture designed, implementation in progress

[![Live Demo](https://img.shields.io/badge/Live%20Demo-Available-blue.svg)](https://react-app-000730.web.app)
[![Firebase](https://img.shields.io/badge/Firebase-Deployed-orange.svg)](https://console.firebase.google.com/project/react-app-000730)
[![API Status](https://img.shields.io/badge/API-Basic%20Functions-yellow.svg)](https://australia-southeast1-react-app-000730.cloudfunctions.net)
[![Development](https://img.shields.io/badge/Status-Active%20Development-blue.svg)](https://github.com/your-repo/rag-prompt-library)
[![License](https://img.shields.io/badge/License-MIT-blue.svg)](LICENSE)

## 🚀 Features

### 🚀 **Current Implementation Status**

#### **✅ Phase 1: Foundation (DEPLOYED)**
- **🔐 User Authentication**: Firebase Auth with email/password and Google OAuth ✅
- **🏗️ Project Structure**: React 18 + TypeScript + Vite + Tailwind CSS ✅
- **☁️ Firebase Integration**: Hosting, Functions, Firestore, Storage configured ✅
- **📱 Responsive UI**: Modern, mobile-friendly interface framework ✅
- **🔧 Development Environment**: Local development and deployment pipeline ✅

#### **🚧 Phase 2: Core Features (IN DEVELOPMENT)**
- **📝 Prompt Management**: Basic CRUD operations (Frontend ready, Backend in progress)
- **🤖 AI Integration**: OpenRouter.ai connection framework (Mock responses currently)
- **📄 Document Upload**: File upload UI ready (Processing pipeline planned)
- **⚡ Prompt Execution**: Basic execution flow (Full AI integration pending)
- **📊 Analytics**: Dashboard framework (Data collection in development)

#### **📋 Phase 3: Advanced Features (PLANNED)**
- **🔍 RAG Pipeline**: Document processing and semantic search
- **📈 Real-time Analytics**: Comprehensive monitoring and metrics
- **🧪 A/B Testing**: Experiment framework and statistical analysis
- **💰 Cost Optimization**: Multi-provider tracking and recommendations
- **🎯 Advanced Search**: Hybrid search with BM25 + semantic capabilities
- **🔄 Collaboration**: Real-time editing and team features

### 🎯 **Future Roadmap (Phase 4+)**
- **🖼️ Multi-Modal Capabilities**: Image, audio, video processing and search
- **🏢 Enterprise Features**: SSO integration, RBAC, audit logging
- **🧠 Machine Learning**: Adaptive retrieval and continuous model improvement
- **🤖 Advanced AI**: Next-generation search and analysis capabilities
- **🔗 Integrations**: Slack, Discord, Microsoft Teams, advanced APIs

## 🚀 Quick Start

### Live Demo
**Production Application**: [https://react-app-000730.web.app](https://react-app-000730.web.app)

### API Access
**Firebase Functions**: Use Firebase SDK with `httpsCallable()` for function calls
**Region**: `australia-southeast1`
**Available Functions**: `api` (main router function)

### Local Development
```bash
# Clone the repository
git clone https://github.com/your-org/react-app-000730.git
cd react-app-000730

# Install dependencies
cd frontend && npm install
cd ../functions && pip install -r requirements.txt

# Run tests
cd frontend && npm test

# Start development server
cd frontend && npm run dev

# Optional: Start Firebase emulators (for local backend testing)
firebase emulators:start
```

**Local URLs:**
- Frontend: http://localhost:5173
- Firebase Emulator: http://localhost:4000
- Functions: http://localhost:5001

### Production Deployment
```bash
# Build and deploy
cd frontend && npm run build
cd .. && firebase deploy
```

For detailed setup instructions, see [Deployment Guide](docs/DEPLOYMENT_GUIDE.md).

## 🏗️ Architecture

### Technology Stack
- **Frontend**: React 18 + TypeScript + Vite + Tailwind CSS ✅
- **Backend**: Firebase Cloud Functions (Node.js) + Firestore + Authentication + Storage ✅
- **AI Integration**: OpenRouter.ai framework (Full integration in development) 🚧
- **Vector Storage**: Architecture designed (FAISS + hybrid retrieval planned) 📋
- **Testing**: Vitest + Testing Library (Framework ready) ✅
- **Deployment**: Firebase Hosting (Active) + GitHub Actions (Planned) ✅/📋
- **Monitoring**: Firebase Analytics + Performance Monitoring ✅

### Project Structure
```
├── frontend/                 # React frontend application
│   ├── src/
│   │   ├── components/      # Reusable UI components
│   │   ├── pages/          # Page components
│   │   ├── contexts/       # React contexts (Auth, etc.)
│   │   ├── services/       # API and Firebase services
│   │   ├── types/          # TypeScript type definitions
│   │   └── test/           # Test utilities and setup
│   ├── public/             # Static assets
│   └── dist/               # Built application
├── functions/              # Firebase Cloud Functions (Python)
├── docs/                   # Project documentation
├── scripts/                # Deployment and utility scripts
├── firebase.json           # Firebase configuration
├── firestore.rules         # Firestore security rules
└── storage.rules           # Cloud Storage security rules
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- Firebase CLI (`npm install -g firebase-tools`)
- Git

### Installation

#### Option 1: Automated Setup (Recommended)
```bash
# Clone the repository
git clone <repository-url>
cd React-App-000730

# Run automated setup script
# Windows
.\scripts\setup-environment.ps1 development

# Linux/Mac
./scripts/setup-environment.sh development
```

#### Option 2: Manual Setup
1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd React-App-000730
   ```

2. **Install dependencies**
   ```bash
   # Frontend dependencies
   cd frontend
   npm install
   cd ..

   # Backend dependencies (Node.js functions)
   cd functions
   npm install
   cd ..
   ```

3. **Install Firebase CLI**
   ```bash
   npm install -g firebase-tools
   firebase login
   ```

4. **Set up environment variables**
   ```bash
   # Frontend environment
   cp frontend/.env.example frontend/.env

   # Backend environment
   cp functions/.env.example functions/.env

   # Edit both .env files with your configuration
   ```

5. **Configure Firebase project**
   ```bash
   firebase use --add
   # Select your Firebase project
   ```

### Development

1. **Start the development server**
   ```bash
   cd frontend
   npm run dev
   ```

2. **Start Firebase emulators** (optional)
   ```bash
   firebase emulators:start
   ```

3. **Run tests**
   ```bash
   cd frontend
   npm run test
   ```

### Deployment

1. **Build the application**
   ```bash
   cd frontend
   npm run build
   ```

2. **Deploy to Firebase**
   ```bash
   # Deploy to development
   ./scripts/deploy.sh development
   
   # Deploy to staging
   ./scripts/deploy.sh staging
   
   # Deploy to production
   ./scripts/deploy.sh production
   ```

## 📖 Usage

### Creating Your First Prompt

1. **Sign up/Login** using email or Google account
2. **Navigate to Prompts** page
3. **Click "New Prompt"** to create a prompt
4. **Fill in the details**:
   - Title and description
   - Prompt content with variables (use `{{variable_name}}`)
   - Tags and category
   - Variable definitions
5. **Save** your prompt
6. **Execute** the prompt with different inputs

### Document Upload for RAG

1. **Go to Documents** page
2. **Click "Upload Documents"**
3. **Drag and drop** or select files (PDF, TXT, DOC, DOCX, MD)
4. **Wait for processing** - documents will be chunked and indexed
5. **Use RAG** in prompt execution for context-aware responses

### Prompt Execution

1. **Select a prompt** from your library
2. **Click "Execute"** button
3. **Fill in variables** if any are defined
4. **Configure settings** (model, temperature, etc.)
5. **Enable RAG** if you want to use uploaded documents
6. **Click "Execute Prompt"** to get AI response
7. **View results** with performance metrics

## 🧪 Testing

The project includes comprehensive testing setup:

```bash
# Run all tests
npm run test

# Run tests with coverage
npm run test:coverage

# Run tests in UI mode
npm run test:ui
```

## 📊 Performance

### **Current Performance Metrics**
- **Frontend**: Deployed and responsive ✅
- **Firebase Functions**: Basic endpoints operational ✅
- **Authentication**: Working with Google OAuth ✅
- **Database**: Firestore configured and accessible ✅
- **API Response**: Basic health checks functional ✅
- **Development Environment**: Local setup working ✅

### **Development Status**
- **Foundation**: Solid architecture and deployment pipeline ✅
- **UI Framework**: Complete component library and routing ✅
- **Backend Structure**: Firebase integration and basic functions ✅
- **AI Integration**: Framework ready, full implementation in progress 🚧
- **Testing**: Test infrastructure in place 🚧

## 🔒 Security

### Implemented Security Measures
- Firebase Authentication with secure rules
- Firestore security rules for data isolation
- Input validation and sanitization
- HTTPS-only communication
- Environment variable protection

### Security Best Practices
- Regular dependency updates
- Security audits and penetration testing
- Proper error handling without information leakage
- Rate limiting on API endpoints
- Data encryption at rest and in transit

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow TypeScript best practices
- Write tests for new features
- Use conventional commit messages
- Update documentation as needed
- Ensure all tests pass before submitting

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Firebase team for the excellent platform
- LangChain community for RAG capabilities
- React and TypeScript communities
- All beta testers and contributors

## 📞 Support

- **Documentation**: Check the `/docs` folder for detailed guides
- **Issues**: Report bugs and request features via GitHub Issues
- **Discussions**: Join our community discussions
- **Email**: Contact the development team

---

**Built with ❤️ by the PromptLibrary Team**

*Making AI prompt management simple, powerful, and collaborative.*
