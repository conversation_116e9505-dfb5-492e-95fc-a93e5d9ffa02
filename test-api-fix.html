<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 300px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 API Fix Test</h1>
        <p>Testing the new httpApi function for document status</p>
    </div>

    <div class="container">
        <h3>🌐 API Endpoint Test</h3>
        <p>Testing the new API routing to fix the spinning icon issue:</p>
        
        <input type="text" id="jobId" placeholder="Enter Job ID (document ID)" value="test-job-123">
        <br>
        <button class="btn-primary" onclick="testAPIEndpoint()">Test API Endpoint</button>
        <button class="btn-success" onclick="testHealthEndpoint()">Test Health Endpoint</button>
        <button class="btn-danger" onclick="testDirectFunction()">Test Direct Function URL</button>
        
        <div id="testLog" class="log">Ready to test API endpoints...</div>
    </div>

    <div class="container">
        <h3>📊 Expected vs Actual Behavior</h3>
        
        <div class="success">
            <strong>✅ Expected (Fixed) Behavior:</strong>
            <ul>
                <li>API call returns JSON response</li>
                <li>Status: "processing", "completed", or "failed"</li>
                <li>No HTML content in response</li>
                <li>Proper CORS headers</li>
            </ul>
        </div>
        
        <div class="error">
            <strong>❌ Previous (Broken) Behavior:</strong>
            <ul>
                <li>API call returned HTML page</li>
                <li>Error: "Unexpected token '<', '<!doctype'..."</li>
                <li>Infinite spinning icon</li>
                <li>No proper API response</li>
            </ul>
        </div>
    </div>

    <script>
        function log(message) {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        async function testAPIEndpoint() {
            const jobId = document.getElementById('jobId').value.trim() || 'test-job-123';
            
            log(`🔍 Testing API endpoint for job: ${jobId}`);
            log(`📡 URL: https://rag-prompt-library.web.app/api/ai/document-status/${jobId}`);
            
            try {
                const response = await fetch(`https://rag-prompt-library.web.app/api/ai/document-status/${jobId}`);
                
                log(`📊 Response Status: ${response.status} ${response.statusText}`);
                log(`📋 Response Headers:`);
                for (const [key, value] of response.headers.entries()) {
                    log(`  ${key}: ${value}`);
                }
                
                const contentType = response.headers.get('content-type');
                log(`🔍 Content-Type: ${contentType}`);
                
                if (contentType && contentType.includes('application/json')) {
                    const data = await response.json();
                    log(`✅ JSON Response received:`);
                    log(JSON.stringify(data, null, 2));
                    
                    if (data.success !== undefined) {
                        log(`🎯 API Fix Status: SUCCESS - Proper JSON API response!`);
                    } else {
                        log(`⚠️  Unexpected JSON structure`);
                    }
                } else {
                    const text = await response.text();
                    log(`❌ Non-JSON Response (first 200 chars):`);
                    log(text.substring(0, 200) + '...');
                    
                    if (text.includes('<!doctype') || text.includes('<html>')) {
                        log(`🚨 ISSUE: Still receiving HTML instead of JSON!`);
                    }
                }
                
            } catch (error) {
                log(`❌ Error: ${error.message}`);
                
                if (error.message.includes('Unexpected token')) {
                    log(`🚨 ISSUE: Still getting JSON parsing error - API not fixed yet`);
                }
            }
        }

        async function testHealthEndpoint() {
            log(`🏥 Testing health endpoint...`);
            
            try {
                const response = await fetch('https://rag-prompt-library.web.app/api/health');
                const data = await response.json();
                
                log(`✅ Health endpoint response:`);
                log(JSON.stringify(data, null, 2));
                
            } catch (error) {
                log(`❌ Health endpoint error: ${error.message}`);
            }
        }

        async function testDirectFunction() {
            log(`🔗 Testing direct function URL...`);
            
            const jobId = document.getElementById('jobId').value.trim() || 'test-job-123';
            const directUrl = `https://australia-southeast1-rag-prompt-library.cloudfunctions.net/httpApi/ai/document-status/${jobId}`;
            
            log(`📡 Direct URL: ${directUrl}`);
            
            try {
                const response = await fetch(directUrl);
                const data = await response.json();
                
                log(`✅ Direct function response:`);
                log(JSON.stringify(data, null, 2));
                
            } catch (error) {
                log(`❌ Direct function error: ${error.message}`);
            }
        }

        // Initialize
        log('🚀 API Fix Test Ready');
        log('📋 This test will verify if the API routing fix resolved the spinning icon issue');
        log('🔧 The fix involved:');
        log('  1. Created httpApi Firebase Function');
        log('  2. Added /api/ai/document-status/:jobId endpoint');
        log('  3. Updated Firebase hosting rewrites');
        log('  4. Deployed both functions and hosting');
        log('');
        log('Click "Test API Endpoint" to verify the fix...');
    </script>
</body>
</html>
