<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Processing Admin Tool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .tool-section {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .tool-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: black; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-secondary { background-color: #6c757d; color: white; }
        input, select, textarea {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: inherit;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .status-card {
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .status-pending { background-color: #fff3cd; border: 1px solid #ffeaa7; }
        .status-processing { background-color: #cce5ff; border: 1px solid #74b9ff; }
        .status-completed { background-color: #d4edda; border: 1px solid #00b894; }
        .status-failed { background-color: #f8d7da; border: 1px solid #e17055; }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #00b894;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #e17055;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 Document Processing Admin Tool</h1>
        <p>Diagnose and fix stuck document processing jobs</p>
    </div>

    <div class="container">
        <div class="tool-section">
            <h3>🔍 Job Status Checker</h3>
            <p>Check the status of a specific document processing job:</p>
            
            <input type="text" id="jobIdInput" placeholder="Enter Job ID" style="width: 300px;">
            <button class="btn-primary" onclick="checkJobStatus()">Check Status</button>
            <button class="btn-secondary" onclick="clearLog()">Clear Log</button>
            
            <div id="statusLog" class="log">Ready to check job status...</div>
        </div>

        <div class="tool-section">
            <h3>🚨 Stuck Jobs Detector</h3>
            <p>Find and fix documents that are stuck in processing state:</p>
            
            <button class="btn-warning" onclick="findStuckJobs()">Find Stuck Jobs</button>
            <button class="btn-success" onclick="fixStuckJobs()">Fix All Stuck Jobs</button>
            <button class="btn-danger" onclick="forceCompleteJob()">Force Complete Job</button>
            
            <div class="warning">
                <strong>⚠️ Warning:</strong> The "Fix All Stuck Jobs" function will mark stuck documents as completed. 
                Use with caution in production environments.
            </div>
            
            <div id="stuckJobsLog" class="log">Click "Find Stuck Jobs" to scan for issues...</div>
        </div>

        <div class="tool-section">
            <h3>📊 System Status Overview</h3>
            <button class="btn-primary" onclick="getSystemStatus()">Refresh Status</button>
            
            <div id="systemStatus" class="status-grid">
                <div class="status-card status-pending">
                    <h4>Pending</h4>
                    <div id="pendingCount">-</div>
                </div>
                <div class="status-card status-processing">
                    <h4>Processing</h4>
                    <div id="processingCount">-</div>
                </div>
                <div class="status-card status-completed">
                    <h4>Completed</h4>
                    <div id="completedCount">-</div>
                </div>
                <div class="status-card status-failed">
                    <h4>Failed</h4>
                    <div id="failedCount">-</div>
                </div>
            </div>
        </div>

        <div class="tool-section">
            <h3>🛠️ Manual Job Operations</h3>
            <p>Manually update job status (use with extreme caution):</p>
            
            <input type="text" id="manualJobId" placeholder="Job ID" style="width: 200px;">
            <select id="manualStatus" style="width: 150px;">
                <option value="completed">Completed</option>
                <option value="failed">Failed</option>
                <option value="processing">Processing</option>
            </select>
            <button class="btn-danger" onclick="updateJobStatus()">Update Status</button>
            
            <div class="error">
                <strong>🚨 Danger Zone:</strong> Manual status updates can cause data inconsistency. 
                Only use this if you understand the implications.
            </div>
        </div>
    </div>

    <script>
        function log(message, elementId = 'statusLog') {
            const logElement = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('statusLog').textContent = 'Log cleared...\n';
        }

        async function checkJobStatus() {
            const jobId = document.getElementById('jobIdInput').value.trim();
            if (!jobId) {
                log('ERROR: Please enter a Job ID');
                return;
            }

            log(`Checking status for job: ${jobId}`);
            
            try {
                const response = await fetch(`/api/ai/document-status/${jobId}`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    log(`✅ Job Status: ${data.status}`);
                    log(`📄 Document ID: ${data.document_id || 'N/A'}`);
                    log(`📅 Created: ${data.created_at || 'N/A'}`);
                    log(`🔄 Updated: ${data.updated_at || 'N/A'}`);
                    log(`📊 Progress: ${data.progress || 0}%`);
                    
                    if (data.error_message) {
                        log(`❌ Error: ${data.error_message}`);
                    }
                } else {
                    log(`❌ Error: ${data.error || 'Unknown error'}`);
                }
            } catch (error) {
                log(`❌ Network Error: ${error.message}`);
            }
        }

        async function findStuckJobs() {
            log('🔍 Scanning for stuck jobs...', 'stuckJobsLog');
            
            try {
                // This would need to be implemented as an admin endpoint
                log('⚠️ This feature requires an admin endpoint to list all jobs', 'stuckJobsLog');
                log('💡 Suggestion: Implement /api/admin/jobs endpoint', 'stuckJobsLog');
                
                // Simulated stuck job detection
                const simulatedStuckJobs = [
                    { jobId: 'job_123', status: 'processing', duration: '15 minutes' },
                    { jobId: 'job_456', status: 'extracting', duration: '8 minutes' }
                ];
                
                if (simulatedStuckJobs.length > 0) {
                    log(`🚨 Found ${simulatedStuckJobs.length} potentially stuck jobs:`, 'stuckJobsLog');
                    simulatedStuckJobs.forEach(job => {
                        log(`  - ${job.jobId}: ${job.status} for ${job.duration}`, 'stuckJobsLog');
                    });
                } else {
                    log('✅ No stuck jobs found', 'stuckJobsLog');
                }
            } catch (error) {
                log(`❌ Error scanning for stuck jobs: ${error.message}`, 'stuckJobsLog');
            }
        }

        async function fixStuckJobs() {
            log('🔧 Attempting to fix stuck jobs...', 'stuckJobsLog');
            
            try {
                // This would call the Firebase function to fix stuck documents
                const response = await fetch('/api/admin/fix-stuck-documents', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    log(`✅ Fixed ${data.documentsFixed || 0} stuck documents`, 'stuckJobsLog');
                } else {
                    log(`❌ Error: ${data.error || 'Unknown error'}`, 'stuckJobsLog');
                }
            } catch (error) {
                log(`❌ Error fixing stuck jobs: ${error.message}`, 'stuckJobsLog');
                log('💡 Try using the Firebase console to run the fix function manually', 'stuckJobsLog');
            }
        }

        async function forceCompleteJob() {
            const jobId = document.getElementById('jobIdInput').value.trim();
            if (!jobId) {
                log('ERROR: Please enter a Job ID first', 'stuckJobsLog');
                return;
            }

            if (!confirm(`Are you sure you want to force complete job ${jobId}?`)) {
                return;
            }

            log(`🔧 Force completing job: ${jobId}`, 'stuckJobsLog');
            
            try {
                // This would need to be implemented
                log('⚠️ This feature requires implementation', 'stuckJobsLog');
                log('💡 Suggestion: Add force-complete endpoint', 'stuckJobsLog');
            } catch (error) {
                log(`❌ Error: ${error.message}`, 'stuckJobsLog');
            }
        }

        async function getSystemStatus() {
            log('📊 Fetching system status...');
            
            try {
                // This would need to be implemented as an admin endpoint
                log('⚠️ System status endpoint not implemented');
                log('💡 Suggestion: Implement /api/admin/system-status endpoint');
                
                // Simulated data
                document.getElementById('pendingCount').textContent = '2';
                document.getElementById('processingCount').textContent = '3';
                document.getElementById('completedCount').textContent = '45';
                document.getElementById('failedCount').textContent = '1';
                
                log('✅ Status updated (simulated data)');
            } catch (error) {
                log(`❌ Error fetching system status: ${error.message}`);
            }
        }

        async function updateJobStatus() {
            const jobId = document.getElementById('manualJobId').value.trim();
            const status = document.getElementById('manualStatus').value;
            
            if (!jobId) {
                alert('Please enter a Job ID');
                return;
            }

            if (!confirm(`Are you sure you want to update job ${jobId} to status "${status}"?`)) {
                return;
            }

            log(`🔧 Updating job ${jobId} to status: ${status}`);
            
            try {
                // This would need to be implemented
                log('⚠️ Manual status update endpoint not implemented');
                log('💡 Suggestion: Implement /api/admin/update-job-status endpoint');
            } catch (error) {
                log(`❌ Error: ${error.message}`);
            }
        }

        // Initialize
        log('🚀 Document Processing Admin Tool loaded');
        log('💡 Use the tools above to diagnose and fix document processing issues');
    </script>
</body>
</html>
