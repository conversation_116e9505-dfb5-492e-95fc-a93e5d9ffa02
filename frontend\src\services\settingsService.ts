import { doc, getDoc, setDoc, updateDoc, collection, query, where, getDocs } from 'firebase/firestore';
import { db } from '../config/firebase';
import type { User } from 'firebase/auth';

export interface UserSettings {
  profile: {
    displayName: string;
    email: string;
    avatar?: string;
    timezone: string;
    language: string;
    bio?: string;
    website?: string;
    location?: string;
  };
  notifications: {
    emailNotifications: boolean;
    promptSharing: boolean;
    systemUpdates: boolean;
    weeklyDigest: boolean;
    marketingEmails: boolean;
    securityAlerts: boolean;
  };
  privacy: {
    profileVisibility: 'public' | 'private';
    allowAnalytics: boolean;
    shareUsageData: boolean;
    showOnlineStatus: boolean;
    allowDirectMessages: boolean;
  };
  preferences: {
    theme: 'light' | 'dark' | 'auto';
    defaultModel: string;
    maxTokens: number;
    temperature: number;
    autoSave: boolean;
    compactMode: boolean;
  };
  apiKeys: Array<{
    id: string;
    name: string;
    provider: string;
    masked: string;
    lastUsed: string;
    isActive: boolean;
    createdAt: string;
  }>;
  billing: {
    plan: 'free' | 'pro' | 'enterprise';
    status: 'active' | 'cancelled' | 'past_due';
    currentPeriodEnd: string | null;
    cancelAtPeriodEnd: boolean;
    paymentMethod?: {
      type: string;
      last4: string;
      expiryMonth: number;
      expiryYear: number;
    };
  };
}

export interface ApiKeyData {
  name: string;
  provider: string;
  key: string;
}

class SettingsService {
  private readonly COLLECTION_NAME = 'userSettings';

  /**
   * Get user settings from Firestore
   */
  async getUserSettings(userId: string): Promise<UserSettings | null> {
    try {
      const settingsRef = doc(db, this.COLLECTION_NAME, userId);
      const settingsSnap = await getDoc(settingsRef);

      if (settingsSnap.exists()) {
        return settingsSnap.data() as UserSettings;
      }

      return null;
    } catch (error) {
      console.error('Error fetching user settings:', error);
      throw new Error('Failed to fetch user settings');
    }
  }

  /**
   * Create default settings for a new user
   */
  async createDefaultSettings(user: User): Promise<UserSettings> {
    const defaultSettings: UserSettings = {
      profile: {
        displayName: user.displayName || 'User',
        email: user.email || '',
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        language: 'en',
      },
      notifications: {
        emailNotifications: true,
        promptSharing: false,
        systemUpdates: true,
        weeklyDigest: true,
        marketingEmails: false,
        securityAlerts: true,
      },
      privacy: {
        profileVisibility: 'private',
        allowAnalytics: true,
        shareUsageData: false,
        showOnlineStatus: true,
        allowDirectMessages: true,
      },
      preferences: {
        theme: 'auto',
        defaultModel: 'gpt-3.5-turbo',
        maxTokens: 2048,
        temperature: 0.7,
        autoSave: true,
        compactMode: false,
      },
      apiKeys: [],
      billing: {
        plan: 'free',
        status: 'active',
        currentPeriodEnd: null,
        cancelAtPeriodEnd: false,
      },
    };

    try {
      const settingsRef = doc(db, this.COLLECTION_NAME, user.uid);
      await setDoc(settingsRef, defaultSettings);
      return defaultSettings;
    } catch (error) {
      console.error('Error creating default settings:', error);
      throw new Error('Failed to create default settings');
    }
  }

  /**
   * Update user settings
   */
  async updateUserSettings(userId: string, updates: Partial<UserSettings>): Promise<void> {
    try {
      const settingsRef = doc(db, this.COLLECTION_NAME, userId);
      await updateDoc(settingsRef, updates);
    } catch (error) {
      console.error('Error updating user settings:', error);
      throw new Error('Failed to update user settings');
    }
  }

  /**
   * Add or update an API key
   */
  async addApiKey(userId: string, apiKeyData: ApiKeyData): Promise<string> {
    try {
      const settings = await this.getUserSettings(userId);
      if (!settings) {
        throw new Error('User settings not found');
      }

      const newApiKey = {
        id: crypto.randomUUID(),
        name: apiKeyData.name,
        provider: apiKeyData.provider,
        masked: this.maskApiKey(apiKeyData.key),
        lastUsed: new Date().toISOString(),
        isActive: true,
        createdAt: new Date().toISOString(),
      };

      const updatedApiKeys = [...settings.apiKeys, newApiKey];
      await this.updateUserSettings(userId, { apiKeys: updatedApiKeys });

      // Store the actual API key securely (in a separate collection with encryption)
      await this.storeApiKeySecurely(userId, newApiKey.id, apiKeyData.key);

      return newApiKey.id;
    } catch (error) {
      console.error('Error adding API key:', error);
      throw new Error('Failed to add API key');
    }
  }

  /**
   * Remove an API key
   */
  async removeApiKey(userId: string, apiKeyId: string): Promise<void> {
    try {
      const settings = await this.getUserSettings(userId);
      if (!settings) {
        throw new Error('User settings not found');
      }

      const updatedApiKeys = settings.apiKeys.filter(key => key.id !== apiKeyId);
      await this.updateUserSettings(userId, { apiKeys: updatedApiKeys });

      // Remove the securely stored API key
      await this.removeStoredApiKey(userId, apiKeyId);
    } catch (error) {
      console.error('Error removing API key:', error);
      throw new Error('Failed to remove API key');
    }
  }

  /**
   * Mask API key for display
   */
  maskApiKey(key: string): string {
    if (key.length <= 8) {
      return '*'.repeat(key.length);
    }
    return key.substring(0, 4) + '*'.repeat(key.length - 8) + key.substring(key.length - 4);
  }

  /**
   * Store API key securely (placeholder - implement proper encryption)
   */
  private async storeApiKeySecurely(userId: string, keyId: string, key: string): Promise<void> {
    // TODO: Implement proper encryption and secure storage
    // This is a placeholder - in production, use proper encryption
    console.log('Storing API key securely:', { userId, keyId, keyLength: key.length });
  }

  /**
   * Remove securely stored API key
   */
  private async removeStoredApiKey(userId: string, keyId: string): Promise<void> {
    // TODO: Implement removal of securely stored API key
    console.log('Removing stored API key:', { userId, keyId });
  }

  /**
   * Get or create user settings
   */
  async getOrCreateUserSettings(user: User): Promise<UserSettings> {
    let settings = await this.getUserSettings(user.uid);
    
    if (!settings) {
      settings = await this.createDefaultSettings(user);
    }

    return settings;
  }

  /**
   * Validate settings data
   */
  validateSettings(settings: Partial<UserSettings>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (settings.profile) {
      if (settings.profile.displayName && settings.profile.displayName.trim().length === 0) {
        errors.push('Display name cannot be empty');
      }
      if (settings.profile.displayName && settings.profile.displayName.length > 100) {
        errors.push('Display name cannot exceed 100 characters');
      }
    }

    if (settings.preferences) {
      if (settings.preferences.maxTokens && (settings.preferences.maxTokens < 1 || settings.preferences.maxTokens > 8192)) {
        errors.push('Max tokens must be between 1 and 8192');
      }
      if (settings.preferences.temperature && (settings.preferences.temperature < 0 || settings.preferences.temperature > 2)) {
        errors.push('Temperature must be between 0 and 2');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

export const settingsService = new SettingsService();
