<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Prompt Saving - RAG Prompt Library</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        .warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 RAG Prompt Library - Debug Test</h1>
        <p>This page tests the prompt saving functionality of the RAG Prompt Library application.</p>
        
        <div class="test-section">
            <h3>📋 Test Instructions</h3>
            <ol>
                <li>Open the main application at <a href="http://localhost:3000" target="_blank">http://localhost:3000</a></li>
                <li>Open the browser developer console (F12)</li>
                <li>Run the test functions below</li>
                <li>Check the console output for detailed logs</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🔧 Quick Tests</h3>
            <button onclick="testFirebaseConfig()">Test Firebase Config</button>
            <button onclick="testAuthentication()">Test Authentication</button>
            <button onclick="testPromptSaving()">Test Prompt Saving</button>
            <button onclick="runFullTest()">Run Full Test Suite</button>
        </div>

        <div class="test-section">
            <h3>📊 Test Results</h3>
            <div id="log" class="log">
                <div class="info">Ready to run tests...</div>
            </div>
        </div>

        <div class="test-section">
            <h3>🔍 Manual Testing Steps</h3>
            <ol>
                <li><strong>Authentication Test:</strong>
                    <ul>
                        <li>Go to <a href="http://localhost:3000" target="_blank">localhost:3000</a></li>
                        <li>If not logged in, sign up or log in with Google</li>
                        <li>Verify you reach the dashboard</li>
                    </ul>
                </li>
                <li><strong>Prompt Creation Test:</strong>
                    <ul>
                        <li>Navigate to the Prompts page</li>
                        <li>Click "Create from Scratch" or "AI-Assisted Creation"</li>
                        <li>Fill in the prompt details</li>
                        <li>Click "Save Prompt"</li>
                        <li>Check if the prompt appears in the list</li>
                    </ul>
                </li>
                <li><strong>Console Debugging:</strong>
                    <ul>
                        <li>Open browser console (F12)</li>
                        <li>Look for any error messages</li>
                        <li>Run: <code>window.runComprehensiveTest()</code></li>
                    </ul>
                </li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🚨 Common Issues to Check</h3>
            <ul>
                <li><strong>Authentication Issues:</strong> User not logged in or authentication state not persisted</li>
                <li><strong>Firestore Rules:</strong> Security rules preventing write access</li>
                <li><strong>Network Issues:</strong> CORS errors or network connectivity problems</li>
                <li><strong>Configuration Issues:</strong> Missing or incorrect Firebase configuration</li>
                <li><strong>Code Errors:</strong> JavaScript errors in the prompt saving logic</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🔗 Useful Links</h3>
            <ul>
                <li><a href="http://localhost:3000" target="_blank">Main Application</a></li>
                <li><a href="https://rag-prompt-library.web.app" target="_blank">Production Application</a></li>
                <li><a href="https://console.firebase.google.com/project/rag-prompt-library" target="_blank">Firebase Console</a></li>
            </ul>
        </div>
    </div>

    <script>
        function log(level, message, data = null) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = level.toLowerCase();
            
            let logEntry = `<div class="${className}">[${timestamp}] [${level.toUpperCase()}] ${message}`;
            if (data) {
                logEntry += `<br><pre>${JSON.stringify(data, null, 2)}</pre>`;
            }
            logEntry += '</div>';
            
            logDiv.innerHTML += logEntry;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function testFirebaseConfig() {
            log('INFO', 'Testing Firebase configuration...');
            
            // This will only work if the main app is open in another tab
            if (window.opener && window.opener.testFirebaseConnection) {
                window.opener.testFirebaseConnection();
                log('SUCCESS', 'Firebase config test initiated in main app');
            } else {
                log('WARNING', 'Please open the main app first and run: window.debugPromptSaving.testFirebaseConfig()');
            }
        }

        function testAuthentication() {
            log('INFO', 'Testing authentication...');
            log('WARNING', 'Please open the main app and run: window.debugPromptSaving.testAuthentication()');
        }

        function testPromptSaving() {
            log('INFO', 'Testing prompt saving...');
            log('WARNING', 'Please open the main app and run: window.debugPromptSaving.testPromptCreation()');
        }

        function runFullTest() {
            log('INFO', 'Running full test suite...');
            log('WARNING', 'Please open the main app and run: window.runComprehensiveTest()');
        }

        // Initial setup
        log('INFO', 'Debug test page loaded');
        log('INFO', 'Open the main application and use the browser console to run tests');
    </script>
</body>
</html>
