import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDocs, 
  getDoc,
  query, 
  where, 
  orderBy,
  limit as firestoreLimit,
  startAfter,
  Timestamp 
} from 'firebase/firestore';
import { db } from '../config/firebase';

export interface TemplateCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  count: number;
}

export interface TemplateAuthor {
  uid: string;
  displayName: string;
  email: string;
  avatar?: string;
  verified: boolean;
  rating: number;
  totalTemplates: number;
}

export interface TemplateRating {
  userId: string;
  rating: number;
  review?: string;
  createdAt: Date;
}

export interface Template {
  id: string;
  title: string;
  description: string;
  content: string;
  category: string;
  tags: string[];
  author: TemplateAuthor;
  isPublic: boolean;
  isPremium: boolean;
  price?: number;
  
  // Usage stats
  downloads: number;
  likes: number;
  views: number;
  
  // Ratings
  rating: number;
  ratingCount: number;
  ratings: TemplateRating[];
  
  // Metadata
  variables: Array<{
    name: string;
    type: string;
    description: string;
    required: boolean;
    default?: string;
  }>;
  
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
  publishedAt?: Date;
  
  // Moderation
  status: 'draft' | 'pending' | 'approved' | 'rejected';
  moderationNotes?: string;
}

export interface MarketplaceFilters {
  category?: string;
  tags?: string[];
  author?: string;
  isPremium?: boolean;
  minRating?: number;
  sortBy?: 'popular' | 'recent' | 'rating' | 'downloads';
  search?: string;
}

export interface MarketplaceResponse {
  templates: Template[];
  total: number;
  hasMore: boolean;
  nextCursor?: string;
}

class MarketplaceService {
  private templatesCollection = collection(db, 'marketplace_templates');
  private categoriesCollection = collection(db, 'template_categories');
  private ratingsCollection = collection(db, 'template_ratings');

  // Get featured templates
  async getFeaturedTemplates(limit: number = 6): Promise<Template[]> {
    try {
      // Simplified query without complex ordering to avoid index requirements
      const q = query(
        this.templatesCollection,
        where('status', '==', 'approved'),
        where('isPublic', '==', true),
        firestoreLimit(limit)
      );

      const snapshot = await getDocs(q);
      return this.mapTemplateDocs(snapshot.docs);
    } catch (error) {
      console.error('Error fetching featured templates:', error);
      // Return empty array instead of throwing error for better UX
      return [];
    }
  }

  // Search and filter templates
  async searchTemplates(
    filters: MarketplaceFilters = {},
    limit: number = 20,
    cursor?: string
  ): Promise<MarketplaceResponse> {
    try {
      let q = query(
        this.templatesCollection,
        where('status', '==', 'approved'),
        where('isPublic', '==', true)
      );

      // Apply filters
      if (filters.category) {
        q = query(q, where('category', '==', filters.category));
      }

      if (filters.isPremium !== undefined) {
        q = query(q, where('isPremium', '==', filters.isPremium));
      }

      if (filters.author) {
        q = query(q, where('author.uid', '==', filters.author));
      }

      // Apply sorting
      switch (filters.sortBy) {
        case 'popular':
          q = query(q, orderBy('downloads', 'desc'));
          break;
        case 'recent':
          q = query(q, orderBy('publishedAt', 'desc'));
          break;
        case 'rating':
          q = query(q, orderBy('rating', 'desc'));
          break;
        default:
          q = query(q, orderBy('downloads', 'desc'));
      }

      // Apply pagination
      q = query(q, firestoreLimit(limit + 1)); // Get one extra to check if there are more

      if (cursor) {
        const cursorDoc = await getDoc(doc(this.templatesCollection, cursor));
        if (cursorDoc.exists()) {
          q = query(q, startAfter(cursorDoc));
        }
      }

      const snapshot = await getDocs(q);
      const templates = this.mapTemplateDocs(snapshot.docs.slice(0, limit));
      const hasMore = snapshot.docs.length > limit;
      const nextCursor = hasMore ? snapshot.docs[limit - 1].id : undefined;

      // Apply client-side filters that can't be done in Firestore
      let filteredTemplates = templates;

      if (filters.tags && filters.tags.length > 0) {
        filteredTemplates = templates.filter(template =>
          filters.tags!.some(tag => template.tags.includes(tag))
        );
      }

      if (filters.minRating) {
        filteredTemplates = templates.filter(template =>
          template.rating >= filters.minRating!
        );
      }

      if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        filteredTemplates = templates.filter(template =>
          template.title.toLowerCase().includes(searchTerm) ||
          template.description.toLowerCase().includes(searchTerm) ||
          template.tags.some(tag => tag.toLowerCase().includes(searchTerm))
        );
      }

      return {
        templates: filteredTemplates,
        total: filteredTemplates.length,
        hasMore,
        nextCursor
      };
    } catch (error) {
      console.error('Error searching templates:', error);
      // Return empty result instead of throwing error for better UX
      return {
        templates: [],
        total: 0,
        hasMore: false
      };
    }
  }

  // Get template by ID
  async getTemplate(templateId: string): Promise<Template | null> {
    try {
      const docRef = doc(this.templatesCollection, templateId);
      const snapshot = await getDoc(docRef);
      
      if (!snapshot.exists()) {
        return null;
      }

      return this.mapTemplateDoc(snapshot);
    } catch (error) {
      console.error('Error fetching template:', error);
      throw new Error('Failed to fetch template');
    }
  }

  // Publish template to marketplace
  async publishTemplate(userId: string, templateData: Partial<Template>): Promise<string> {
    try {
      const template = {
        ...templateData,
        author: {
          uid: userId,
          // Would fetch user data from users collection
          displayName: 'User Name',
          email: '<EMAIL>',
          verified: false,
          rating: 0,
          totalTemplates: 0
        },
        downloads: 0,
        likes: 0,
        views: 0,
        rating: 0,
        ratingCount: 0,
        ratings: [],
        status: 'pending',
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        publishedAt: Timestamp.now()
      };

      const docRef = await addDoc(this.templatesCollection, template);
      return docRef.id;
    } catch (error) {
      console.error('Error publishing template:', error);
      throw new Error('Failed to publish template');
    }
  }

  // Rate template
  async rateTemplate(templateId: string, userId: string, rating: number, review?: string): Promise<void> {
    try {
      // Add rating to ratings collection
      await addDoc(this.ratingsCollection, {
        templateId,
        userId,
        rating,
        review,
        createdAt: Timestamp.now()
      });

      // Update template's average rating
      const template = await this.getTemplate(templateId);
      if (template) {
        const newRatingCount = template.ratingCount + 1;
        const newRating = ((template.rating * template.ratingCount) + rating) / newRatingCount;

        await updateDoc(doc(this.templatesCollection, templateId), {
          rating: newRating,
          ratingCount: newRatingCount,
          updatedAt: Timestamp.now()
        });
      }
    } catch (error) {
      console.error('Error rating template:', error);
      throw new Error('Failed to rate template');
    }
  }

  // Download template
  async downloadTemplate(templateId: string): Promise<void> {
    try {
      const docRef = doc(this.templatesCollection, templateId);
      const template = await getDoc(docRef);
      
      if (template.exists()) {
        await updateDoc(docRef, {
          downloads: (template.data().downloads || 0) + 1,
          updatedAt: Timestamp.now()
        });
      }
    } catch (error) {
      console.error('Error downloading template:', error);
      throw new Error('Failed to download template');
    }
  }

  // Get categories
  async getCategories(): Promise<TemplateCategory[]> {
    try {
      const snapshot = await getDocs(this.categoriesCollection);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as TemplateCategory[];
    } catch (error) {
      console.error('Error fetching categories:', error);
      // Return default categories instead of throwing error
      return [
        { id: 'writing', name: 'Writing & Content', description: 'Content creation templates', icon: '✍️', count: 0 },
        { id: 'coding', name: 'Code & Development', description: 'Programming templates', icon: '💻', count: 0 },
        { id: 'analysis', name: 'Data Analysis', description: 'Analysis templates', icon: '📊', count: 0 },
        { id: 'business', name: 'Business & Strategy', description: 'Business templates', icon: '💼', count: 0 }
      ];
    }
  }

  // Get user's published templates
  async getUserTemplates(userId: string): Promise<Template[]> {
    try {
      const q = query(
        this.templatesCollection,
        where('author.uid', '==', userId),
        orderBy('createdAt', 'desc')
      );

      const snapshot = await getDocs(q);
      return this.mapTemplateDocs(snapshot.docs);
    } catch (error) {
      console.error('Error fetching user templates:', error);
      throw new Error('Failed to fetch user templates');
    }
  }

  // Helper methods
  private mapTemplateDocs(docs: any[]): Template[] {
    return docs.map(doc => this.mapTemplateDoc(doc));
  }

  private mapTemplateDoc(doc: any): Template {
    const data = doc.data();
    return {
      id: doc.id,
      ...data,
      createdAt: data.createdAt?.toDate(),
      updatedAt: data.updatedAt?.toDate(),
      publishedAt: data.publishedAt?.toDate(),
      ratings: data.ratings?.map((rating: any) => ({
        ...rating,
        createdAt: rating.createdAt?.toDate()
      })) || []
    } as Template;
  }
}

export const marketplaceService = new MarketplaceService();
